/**
 * Chat Manager
 * 
 * Manages chat sessions and conversations with AI providers
 */

import { IChatManager } from './interfaces';
import { AIMessage, AIResponse, ChatOptions } from '../types';
import { AIProviderFactory } from './provider-factory';

export class ChatManager implements IChatManager {
  private sessions: Map<string, AIMessage[]> = new Map();
  private providerFactory: AIProviderFactory;

  constructor() {
    this.providerFactory = AIProviderFactory.getInstance();
  }

  async startSession(sessionId?: string): Promise<string> {
    const id = sessionId || this.generateSessionId();
    this.sessions.set(id, []);
    return id;
  }

  async sendMessage(sessionId: string, message: string, options?: ChatOptions): Promise<AIResponse> {
    if (!this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const history = this.sessions.get(sessionId)!;
    const userMessage: AIMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    history.push(userMessage);

    // Create provider with default configuration
    const provider = this.providerFactory.createProvider({
      name: 'goc',
      displayName: 'GOC Agent',
      tier: 'paid',
      apiKey: process.env.GROQ_API_KEY || '',
      baseUrl: 'https://api.groq.com/openai/v1',
      defaultModel: 'llama-3.1-70b-versatile',
      models: [{
        id: 'llama-3.1-70b-versatile',
        name: 'Llama 3.1 70B Versatile',
        tier: 'paid',
        contextLength: 32768,
        capabilities: ['text-generation', 'code-generation'],
        requiresAuth: true,
        isLocal: false
      }],
      maxTokens: 4096,
      temperature: 0.7,
      timeout: 30000,
      requiresAuth: true,
      isLocal: false
    });

    const response = await provider.chat(history, options);

    const assistantMessage: AIMessage = {
      role: 'assistant',
      content: response.content,
      timestamp: new Date()
    };

    history.push(assistantMessage);
    return response;
  }

  getHistory(sessionId: string): AIMessage[] {
    return this.sessions.get(sessionId) || [];
  }

  clearHistory(sessionId: string): void {
    if (this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, []);
    }
  }

  getSessions(): string[] {
    return Array.from(this.sessions.keys());
  }

  endSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
