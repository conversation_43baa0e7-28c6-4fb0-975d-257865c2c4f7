{"version": 3, "file": "model-manager.js", "sourceRoot": "", "sources": ["../../src/ai/model-manager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH,yDAAuD;AAEvD,MAAa,YAAY;IAIvB;QAFQ,eAAU,GAA6B,IAAI,GAAG,EAAE,CAAC;QAGvD,IAAI,CAAC,eAAe,GAAG,oCAAiB,CAAC,WAAW,EAAE,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;QAC/D,MAAM,SAAS,GAAgB,EAAE,CAAC;QAElC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBACzD,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,qCAAqC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACzC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACxC,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,KAAa;QAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,KAAa;QACpD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,SAAS,EAAE,WAAW,IAAI,KAAK,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,IAAe;QACvC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAE5C,mCAAmC;QACnC,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC9C,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAC5D,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oEAAoE;QACpE,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,2BAA2B,CAAC,QAAgB;QAClD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,OAAO;oBACL;wBACE,EAAE,EAAE,yBAAyB;wBAC7B,IAAI,EAAE,yBAAyB;wBAC/B,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,MAAM;wBACZ,aAAa,EAAE,KAAK;wBACpB,YAAY,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,aAAa,EAAE,cAAc,CAAC;wBACpF,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,KAAK;qBACf;oBACD;wBACE,EAAE,EAAE,sBAAsB;wBAC1B,IAAI,EAAE,sBAAsB;wBAC5B,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,MAAM;wBACZ,aAAa,EAAE,KAAK;wBACpB,YAAY,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC;wBACjD,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO;oBACL;wBACE,EAAE,EAAE,OAAO;wBACX,IAAI,EAAE,OAAO;wBACb,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,MAAM;wBACZ,aAAa,EAAE,IAAI;wBACnB,YAAY,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC;wBACjG,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,KAAK;qBACf;oBACD;wBACE,EAAE,EAAE,eAAe;wBACnB,IAAI,EAAE,eAAe;wBACrB,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,MAAM;wBACZ,aAAa,EAAE,IAAI;wBACnB,YAAY,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC;wBACjD,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO;oBACL;wBACE,EAAE,EAAE,iBAAiB;wBACrB,IAAI,EAAE,iBAAiB;wBACvB,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,MAAM;wBACZ,aAAa,EAAE,MAAM;wBACrB,YAAY,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC;wBACjG,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO;oBACL;wBACE,EAAE,EAAE,YAAY;wBAChB,IAAI,EAAE,YAAY;wBAClB,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,MAAM;wBACZ,aAAa,EAAE,KAAK;wBACpB,YAAY,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,CAAC;wBACrE,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG;wBAChB,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC;YAEJ;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,IAAe;QACzC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,iBAAiB,CAAC;YACvB,KAAK,kBAAkB,CAAC;YACxB,KAAK,aAAa,CAAC;YACnB,KAAK,WAAW,CAAC;YACjB,KAAK,aAAa,CAAC;YACnB,KAAK,eAAe,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC;YACd,KAAK,cAAc;gBACjB,OAAO,cAAc,CAAC;YACxB;gBACE,OAAO,cAAc,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AA7KD,oCA6KC"}