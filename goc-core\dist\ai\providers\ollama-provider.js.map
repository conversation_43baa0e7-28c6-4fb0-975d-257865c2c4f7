{"version": 3, "file": "ollama-provider.js", "sourceRoot": "", "sources": ["../../../src/ai/providers/ollama-provider.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,oDAAkD;AAClD,uCAAyG;AACzG,uCAAqC;AAErC,MAAa,cAAe,SAAQ,8BAAc;IAGhD,YACE,SAAiB,EAAE,EAAE,wBAAwB;IAC7C,UAAkB,wBAAwB,EAC1C,eAAuB,aAAa,EACpC,UAAkB,KAAK,CAAC,kCAAkC;;QAE1D,KAAK,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QARlE,oBAAe,GAAa,EAAE,CAAC;QASrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,yBAAyB;IAChD,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,sDAAsD;YACtD,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,cAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,CAAC,eAAe,CAAC,MAAM,SAAS,EAAE,gBAAgB,CAAC,CAAC;QAC1G,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAM,CAAC,IAAI,CAAC,+DAA+D,EAAE,gBAAgB,CAAC,CAAC;YAC/F,uCAAuC;QACzC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc;aACjD,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAqB,EAAE,OAAqB;QACrD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;YAElD,8BAA8B;YAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,uBAAe,CACvB,SAAS,KAAK,wDAAwD,KAAK,EAAE,EAC7E,IAAI,CAAC,IAAI,CACV,CAAC;YACJ,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAE5D,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;oBACxC,KAAK,EAAE,OAAO,EAAE,IAAI,IAAI,GAAG;oBAC3B,UAAU,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;iBACvC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACjC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACxC,MAAM,IAAI,uBAAe,CACvB,qBAAqB,QAAQ,CAAC,MAAM,MAAM,SAAS,EAAE,EACrD,IAAI,CAAC,IAAI,CACV,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAE1C,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE;gBACpC,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE;oBACL,YAAY,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC;oBACzC,gBAAgB,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;oBACtC,WAAW,EAAE,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;iBACpE;gBACD,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBAC3C,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,KAAK,EAAE,KAAK;oBACZ,cAAc,EAAE,IAAI;oBACpB,OAAO,EAAE,UAAU;oBACnB,IAAI,EAAE,CAAC,CAAC,uBAAuB;iBAChC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,uBAAe,EAAE,CAAC;gBACrC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,uBAAe,CACvB,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACzF,IAAI,CAAC,IAAI,EACT,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CACd,QAAqB,EACrB,OAAqB,EACrB,OAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;YAElD,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,uBAAe,CACvB,SAAS,KAAK,wDAAwD,KAAK,EAAE,EAC7E,IAAI,CAAC,IAAI,CACV,CAAC;YACJ,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAE5D,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE;oBACP,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;oBACxC,KAAK,EAAE,OAAO,EAAE,IAAI,IAAI,GAAG;oBAC3B,UAAU,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;iBACvC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACjC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACxC,MAAM,IAAI,uBAAe,CACvB,qBAAqB,QAAQ,CAAC,MAAM,MAAM,SAAS,EAAE,EACrD,IAAI,CAAC,IAAI,CACV,CAAC;YACJ,CAAC;YAED,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAEzB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,uBAAe,CAAC,4BAA4B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAElC,IAAI,CAAC;gBACH,OAAO,IAAI,EAAE,CAAC;oBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBAE5C,IAAI,IAAI;wBAAE,MAAM;oBAEhB,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACpC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAE5D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,IAAI,CAAC;4BACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAE9B,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;gCAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gCACrC,WAAW,IAAI,OAAO,CAAC;gCACvB,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;4BACrB,CAAC;4BAED,IAAI,IAAI,CAAC,iBAAiB;gCAAE,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC;4BAClE,IAAI,IAAI,CAAC,UAAU;gCAAE,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;4BAExD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gCACd,WAAW,GAAG,YAAY,GAAG,gBAAgB,CAAC;gCAC9C,MAAM;4BACR,CAAC;wBACH,CAAC;wBAAC,OAAO,UAAU,EAAE,CAAC;4BACpB,0BAA0B;4BAC1B,SAAS;wBACX,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE;oBACL,YAAY;oBACZ,gBAAgB;oBAChB,WAAW;iBACZ;gBACD,YAAY,EAAE,MAAM;gBACpB,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,KAAK,EAAE,KAAK;oBACZ,cAAc,EAAE,IAAI;oBACpB,OAAO,EAAE,UAAU;oBACnB,IAAI,EAAE,CAAC;iBACR;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,uBAAe,EAAE,CAAC;gBACrC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,uBAAe,CACvB,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAChG,IAAI,CAAC,IAAI,EACT,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpC,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,iBAAiB;QAarB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;aACnC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;gBAC1C,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACvF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,SAAiB;QAClC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gBACzC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;aACnC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAM,CAAC,KAAK,CAAC,gCAAgC,SAAS,EAAE,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACvF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,4CAA4C;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,0DAA0D;oBACjE,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEpC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mEAAmE;oBAC1E,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,sBAAsB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;gBACvF,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,SAAiB;QAC/B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gBACzC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,+BAA+B;aACpE,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,EAAE,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;gBACzD,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACtC,CAAC;YAED,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,cAAM,CAAC,KAAK,CAAC,0BAA0B,SAAS,EAAE,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACjF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,oBAAoB;QAClB,OAAO;;;;;;;;;;;;;;;;;;;;;;KAsBN,CAAC;IACJ,CAAC;IAED,yBAAyB;IACjB,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBACvD,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;gBAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAE1E,qCAAqC;gBACrC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpC,cAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;gBAC1H,CAAC;qBAAM,CAAC;oBACN,cAAM,CAAC,IAAI,CAAC,qEAAqE,EAAE,gBAAgB,CAAC,CAAC;gBACvG,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;gBAC1B,cAAM,CAAC,IAAI,CAAC,2DAA2D,EAAE,gBAAgB,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;YAC1B,cAAM,CAAC,IAAI,CAAC,qEAAqE,EAAE,gBAAgB,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC9C,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpC,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;IAEO,qBAAqB,CAAC,QAAqB;QACjD,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;IACN,CAAC;CACF;AAlbD,wCAkbC"}