/**
 * GOC Core - Main Entry Point
 * 
 * The core library for the GOC Agent ecosystem, providing AI providers,
 * context engine, file operations, web research, and configuration management.
 */

// Export all types
export * from './types';

// Export all modules
export * from './ai';
export { ContextEngine } from './context';
export * from './files';
export { WebResearcher } from './web';
export * from './config';
export { LearningEngine } from './learning';
export * from './auto';
export * from './generation';
export { MonitoringSystem } from './monitoring';
export * from './documentation';
export * from './deployment';
export * from './utils';

// Main GOC Core class
import { GocCoreOptions, GocConfig, AIMessage, AIResponse } from './types';
import { ConfigLoader } from './config/config-loader';
import { getDefaultConfig } from './config/default-config';
import { LearningEngine } from './learning/learning-engine';
import { ContextEngine } from './context';
import { WebResearcher } from './web';
import { AutoModeEngine } from './auto';
import { CodeGenerator } from './generation';
import { MonitoringSystem } from './monitoring';
import { DocumentationGenerator } from './documentation';
import { DeploymentAssistant } from './deployment';

export class GocCore {
  private config: GocConfig;
  private configLoader: ConfigLoader;
  private learningEngine?: LearningEngine;
  private contextEngine?: ContextEngine;
  private webResearcher?: WebResearcher;
  private autoModeEngine?: AutoModeEngine;
  private codeGenerator?: CodeGenerator;
  private monitoringSystem?: MonitoringSystem;
  private documentationGenerator?: DocumentationGenerator;
  private deploymentAssistant?: DeploymentAssistant;
  private initialized: boolean = false;

  constructor(options?: GocCoreOptions) {
    this.configLoader = new ConfigLoader();
    this.config = this.mergeConfig(getDefaultConfig(), options);
  }

  async initialize(): Promise<void> {
    // Load configuration from files
    const fileConfig = await this.configLoader.loadConfig();
    this.config = this.mergeConfig(this.config, fileConfig as GocCoreOptions);

    // Initialize learning engine if enabled
    if (this.config.learning.enabled) {
      try {
        this.learningEngine = new LearningEngine({
          config: this.config,
          enableAutoLearning: this.config.learning.autoLearn
        });
        await this.learningEngine.initialize();
      } catch (error) {
        console.warn('Failed to initialize learning engine:', error);
      }
    }

    // Initialize context engine
    try {
      this.contextEngine = new ContextEngine();
    } catch (error) {
      console.warn('Failed to initialize context engine:', error);
      // Create a minimal stub
      this.contextEngine = {} as ContextEngine;
    }

    // Initialize web researcher
    try {
      this.webResearcher = new WebResearcher();
    } catch (error) {
      console.warn('Failed to initialize web researcher:', error);
      // Create a minimal stub
      this.webResearcher = {} as WebResearcher;
    }

    // Initialize code generator
    try {
      this.codeGenerator = new CodeGenerator(this.contextEngine);
    } catch (error) {
      console.warn('Failed to initialize code generator:', error);
      // Create a minimal stub
      this.codeGenerator = {} as CodeGenerator;
    }

    // Initialize auto mode engine
    try {
      this.autoModeEngine = new AutoModeEngine(
        this.contextEngine,
        this.webResearcher,
        {
          enableLearning: this.config.learning.enabled,
          enableWebResearch: this.config.web.enabled,
          enableFileOperations: false, // Disabled by default for safety
          safeMode: true
        }
      );
    } catch (error) {
      console.warn('Failed to initialize auto mode engine:', error);
      // Create a minimal stub
      this.autoModeEngine = {} as AutoModeEngine;
    }

    // Initialize monitoring system
    try {
      this.monitoringSystem = new MonitoringSystem({
        enablePerformanceTracking: true,
        enableUsageAnalytics: true,
        enableErrorTracking: true,
        retentionDays: 30
      });
    } catch (error) {
      console.warn('Failed to initialize monitoring system:', error);
      // Create a minimal stub
      this.monitoringSystem = {} as MonitoringSystem;
    }

    // Initialize documentation generator
    try {
      this.documentationGenerator = new DocumentationGenerator(this.contextEngine);
    } catch (error) {
      console.warn('Failed to initialize documentation generator:', error);
      // Create a minimal stub
      this.documentationGenerator = {} as DocumentationGenerator;
    }

    // Initialize deployment assistant
    try {
      this.deploymentAssistant = new DeploymentAssistant();
    } catch (error) {
      console.warn('Failed to initialize deployment assistant:', error);
      // Create a minimal stub
      this.deploymentAssistant = {} as DeploymentAssistant;
    }

    this.initialized = true;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  async dispose(): Promise<void> {
    if (this.learningEngine) {
      await this.learningEngine.dispose();
    }
    this.initialized = false;
  }

  getConfig(): GocConfig {
    return { ...this.config };
  }

  updateConfig(updates: GocCoreOptions): void {
    this.config = this.mergeConfig(this.config, updates);
  }

  /**
   * Get the learning engine instance
   */
  getLearningEngine(): LearningEngine | undefined {
    return this.learningEngine;
  }

  /**
   * Get the context engine instance
   */
  getContextEngine(): ContextEngine {
    if (!this.contextEngine) {
      throw new Error('Context engine not initialized. Call initialize() first.');
    }
    return this.contextEngine;
  }

  /**
   * Get the web researcher instance
   */
  getWebResearcher(): WebResearcher {
    if (!this.webResearcher) {
      throw new Error('Web researcher not initialized. Call initialize() first.');
    }
    return this.webResearcher;
  }

  /**
   * Get the auto mode engine instance
   */
  getAutoModeEngine(): AutoModeEngine {
    if (!this.autoModeEngine) {
      throw new Error('Auto mode engine not initialized. Call initialize() first.');
    }
    return this.autoModeEngine;
  }

  /**
   * Get the code generator instance
   */
  getCodeGenerator(): CodeGenerator {
    if (!this.codeGenerator) {
      throw new Error('Code generator not initialized. Call initialize() first.');
    }
    return this.codeGenerator;
  }

  /**
   * Get the monitoring system instance
   */
  getMonitoringSystem(): MonitoringSystem {
    if (!this.monitoringSystem) {
      throw new Error('Monitoring system not initialized. Call initialize() first.');
    }
    return this.monitoringSystem;
  }

  /**
   * Get the documentation generator instance
   */
  getDocumentationGenerator(): DocumentationGenerator {
    if (!this.documentationGenerator) {
      throw new Error('Documentation generator not initialized. Call initialize() first.');
    }
    return this.documentationGenerator;
  }

  /**
   * Get the deployment assistant instance
   */
  getDeploymentAssistant(): DeploymentAssistant {
    if (!this.deploymentAssistant) {
      throw new Error('Deployment assistant not initialized. Call initialize() first.');
    }
    return this.deploymentAssistant;
  }

  /**
   * Learn from AI interaction
   */
  async learnFromInteraction(
    messages: AIMessage[],
    response: AIResponse,
    userFeedback?: {
      accepted: boolean;
      modifications?: string;
      rating?: number;
    }
  ): Promise<void> {
    if (this.learningEngine) {
      await this.learningEngine.learnFromInteraction(messages, response, userFeedback);
    }
  }

  /**
   * Enhance messages with learned context
   */
  async enhanceMessages(messages: AIMessage[]): Promise<AIMessage[]> {
    if (this.learningEngine) {
      return await this.learningEngine.enhanceMessages(messages);
    }
    return messages;
  }

  /**
   * Learn from web research
   */
  async learnFromWebResearch(
    query: string,
    results: any[],
    relevantContent: string[]
  ): Promise<void> {
    if (this.learningEngine) {
      await this.learningEngine.learnFromWebResearch(query, results, relevantContent);
    }
  }

  /**
   * Get learning metrics
   */
  async getLearningMetrics(): Promise<any> {
    if (this.learningEngine) {
      return await this.learningEngine.getMetrics();
    }
    return null;
  }

  private mergeConfig(base: GocConfig, updates?: GocCoreOptions): GocConfig {
    if (!updates) return base;

    return {
      ...base,
      ai: updates.ai ? { ...base.ai, ...updates.ai } : base.ai,
      context: updates.context ? { ...base.context, ...updates.context } : base.context,
      files: updates.files ? { ...base.files, ...updates.files } : base.files,
      web: updates.web ? { ...base.web, ...updates.web } : base.web,
      learning: updates.learning ? { ...base.learning, ...updates.learning } : base.learning,
      global: updates.global ? { ...base.global, ...updates.global } : base.global
    };
  }
}

// Factory function for creating GOC Core instances
export async function createGocCore(options?: GocCoreOptions): Promise<GocCore> {
  const core = new GocCore(options);
  await core.initialize();
  return core;
}
