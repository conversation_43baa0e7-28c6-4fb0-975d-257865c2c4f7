/**
 * Ollama Provider
 *
 * Local AI provider for Ollama models - completely offline and private
 */
import { BaseAIProvider } from '../base-provider';
import { AIMessage, AIResponse, ChatOptions, BaseResult } from '../../types';
export declare class OllamaProvider extends BaseAIProvider {
    private availableModels;
    constructor(apiKey?: string, // Not needed for Ollama
    baseUrl?: string, defaultModel?: string, timeout?: number);
    initialize(): Promise<void>;
    isAvailable(): Promise<boolean>;
    chat(messages: AIMessage[], options?: ChatOptions): Promise<AIResponse>;
    streamChat(messages: AIMessage[], options?: ChatOptions, onChunk?: (chunk: string) => void): Promise<AIResponse>;
    getModels(): Promise<string[]>;
    getDetailedModels(): Promise<Array<{
        name: string;
        size: string;
        digest: string;
        modified: string;
        details?: {
            format: string;
            family: string;
            families?: string[];
            parameter_size: string;
            quantization_level: string;
        };
    }>>;
    getModelInfo(modelName: string): Promise<any>;
    validateApiKey(): Promise<boolean>;
    validateConnection(): Promise<BaseResult>;
    pullModel(modelName: string): Promise<boolean>;
    deleteModel(modelName: string): Promise<boolean>;
    getSetupInstructions(): string;
    private refreshAvailableModels;
    private isModelAvailable;
    private convertToOllamaFormat;
}
//# sourceMappingURL=ollama-provider.d.ts.map