"use strict";
/**
 * Base AI Provider
 *
 * Abstract base class that provides common functionality for all AI providers
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAIProvider = void 0;
class BaseAIProvider {
    constructor(name, displayName, apiKey, baseUrl, timeout = 30000, defaultModel, maxTokens = 4096, temperature = 0.7) {
        this.name = name;
        this.displayName = displayName;
        this.isLocal = false;
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
        this.timeout = timeout;
        this.defaultModel = defaultModel || '';
        this.maxTokens = maxTokens;
        this.temperature = temperature;
        this.usage = {
            totalRequests: 0,
            totalTokens: 0,
            totalCost: 0,
            lastUsed: new Date(),
            averageResponseTime: 0
        };
    }
    get config() {
        return {
            name: this.name, // Type assertion for provider name
            displayName: this.displayName,
            tier: this.isLocal ? 'free' : 'paid',
            apiKey: this.apiKey,
            baseUrl: this.baseUrl,
            models: [], // Will be populated by getModels()
            defaultModel: this.defaultModel,
            maxTokens: this.maxTokens,
            temperature: this.temperature,
            timeout: this.timeout,
            requiresAuth: !this.isLocal,
            isLocal: this.isLocal
        };
    }
    async isAvailable() {
        try {
            const result = await this.validateConnection();
            return result.success;
        }
        catch {
            return false;
        }
    }
    async initialize() {
        // Default implementation - can be overridden by providers
    }
    async validateConnection() {
        try {
            const isValid = await this.validateApiKey();
            return {
                success: isValid,
                error: isValid ? undefined : 'Invalid API key or connection failed',
                timestamp: new Date()
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date()
            };
        }
    }
    async getUsage() {
        return { ...this.usage };
    }
    async dispose() {
        // Default implementation - can be overridden by providers
    }
    // Protected utility methods for providers
    validateMessages(messages) {
        if (!messages || messages.length === 0) {
            throw new Error('Messages array cannot be empty');
        }
        for (const message of messages) {
            if (!message.role || !message.content) {
                throw new Error('Each message must have role and content');
            }
            if (!['system', 'user', 'assistant'].includes(message.role)) {
                throw new Error(`Invalid message role: ${message.role}`);
            }
        }
    }
    buildHeaders() {
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'User-Agent': 'GOC-Agent/1.0.0'
        };
    }
    async makeRequest(url, options, parser) {
        const startTime = Date.now();
        try {
            const response = await fetch(url, {
                ...options,
                signal: AbortSignal.timeout(this.timeout)
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await parser(response);
            // Update usage statistics
            const duration = Date.now() - startTime;
            this.usage.totalRequests++;
            this.usage.lastUsed = new Date();
            this.usage.averageResponseTime =
                (this.usage.averageResponseTime * (this.usage.totalRequests - 1) + duration) / this.usage.totalRequests;
            return result;
        }
        catch (error) {
            throw new Error(`Request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    getDefaultModel() {
        return this.defaultModel;
    }
    updateUsage(tokens, cost = 0) {
        this.usage.totalTokens += tokens;
        this.usage.totalCost += cost;
    }
}
exports.BaseAIProvider = BaseAIProvider;
//# sourceMappingURL=base-provider.js.map