{"version": 3, "file": "base-provider.d.ts", "sourceRoot": "", "sources": ["../../src/ai/base-provider.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAE5F,8BAAsB,cAAe,YAAW,WAAW;aAWvC,IAAI,EAAE,MAAM;aACZ,WAAW,EAAE,MAAM;IAXrC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,YAAY,EAAE,MAAM,CAAC;IAC/B,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;IAC9B,SAAS,CAAC,KAAK,EAAE,aAAa,CAAC;IAC/B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAS;gBAGjB,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnC,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,MAAc,EACvB,YAAY,CAAC,EAAE,MAAM,EACrB,SAAS,GAAE,MAAa,EACxB,WAAW,GAAE,MAAY;IAiB3B,IAAI,MAAM,IAAI,gBAAgB,CAe7B;IAEK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;IAS/B,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAI3B,kBAAkB,IAAI,OAAO,CAAC,UAAU,CAAC;IAiBzC,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC;IAIlC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAK9B,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;IAChF,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;IACzH,QAAQ,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IACvC,QAAQ,CAAC,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAG3C,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI;IAevD,SAAS,CAAC,YAAY,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;cAQhC,WAAW,CAAC,CAAC,EAC3B,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,WAAW,EACpB,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,GACzC,OAAO,CAAC,CAAC,CAAC;IA4Bb,eAAe,IAAI,MAAM;IAIzB,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,MAAU,GAAG,IAAI;CAI9D"}