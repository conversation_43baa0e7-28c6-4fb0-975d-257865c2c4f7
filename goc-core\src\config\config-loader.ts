/**
 * Configuration Loader
 * 
 * Loads configuration from various sources
 */

import * as fs from 'fs';
import * as path from 'path';
import { GocConfig } from '../types';

export class ConfigLoader {
  private configPaths: string[] = [
    '.gocrc.json',
    '.gocrc.js',
    'goc.config.json',
    'goc.config.js'
  ];

  async loadConfig(configPath?: string): Promise<Partial<GocConfig>> {
    if (configPath) {
      return this.loadFromFile(configPath);
    }

    // Try to find config file in current directory and parent directories
    let currentDir = process.cwd();
    
    while (currentDir !== path.dirname(currentDir)) {
      for (const configFile of this.configPaths) {
        const fullPath = path.join(currentDir, configFile);
        if (fs.existsSync(fullPath)) {
          return this.loadFromFile(fullPath);
        }
      }
      currentDir = path.dirname(currentDir);
    }

    // Load from environment variables
    return this.loadFromEnvironment();
  }

  private async loadFromFile(filePath: string): Promise<Partial<GocConfig>> {
    try {
      const ext = path.extname(filePath);
      
      if (ext === '.json') {
        const content = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(content);
      } else if (ext === '.js') {
        // Dynamic import for JS config files
        const configModule = await import(path.resolve(filePath));
        return configModule.default || configModule;
      }
      
      return {};
    } catch (error) {
      console.warn(`Failed to load config from ${filePath}:`, error);
      return {};
    }
  }

  private loadFromEnvironment(): Partial<GocConfig> {
    return {
      ai: {
        defaultProvider: (process.env.GOC_AI_PROVIDER as any) || 'groq',
        defaultModel: process.env.GOC_AI_MODEL || 'llama-3.1-70b-versatile',
        temperature: parseFloat(process.env.GOC_AI_TEMPERATURE || '0.7'),
        maxTokens: parseInt(process.env.GOC_AI_MAX_TOKENS || '4096'),
        timeout: parseInt(process.env.GOC_AI_TIMEOUT || '30000'),
        providers: {}
      },
      global: {
        logLevel: (process.env.GOC_LOG_LEVEL as any) || 'info',
        cacheDirectory: process.env.GOC_CACHE_DIR || '.goc/cache',
        tempDirectory: process.env.GOC_TEMP_DIR || '.goc/temp',
        enableTelemetry: process.env.GOC_TELEMETRY !== 'false',
        autoUpdate: process.env.GOC_AUTO_UPDATE !== 'false'
      },
      context: {
        maxContextLines: parseInt(process.env.GOC_MAX_CONTEXT_LINES || '1000'),
        enableSemanticSearch: process.env.GOC_SEMANTIC_SEARCH !== 'false',
        cacheEnabled: process.env.GOC_CACHE_ENABLED !== 'false',
        cacheSize: parseInt(process.env.GOC_CACHE_SIZE || '100'),
        analysisDepth: (process.env.GOC_ANALYSIS_DEPTH as any) || 'medium'
      },
      files: {
        maxFileSize: parseInt(process.env.GOC_MAX_FILE_SIZE || '10485760'),
        allowedExtensions: (process.env.GOC_ALLOWED_EXTENSIONS || '.js,.ts,.jsx,.tsx,.py,.php,.html,.css,.md,.json').split(','),
        backupEnabled: process.env.GOC_BACKUP_ENABLED !== 'false',
        backupDirectory: process.env.GOC_BACKUP_DIR || '.goc/backups',
        autoSave: process.env.GOC_AUTO_SAVE !== 'false'
      },
      web: {
        enabled: process.env.GOC_WEB_ENABLED !== 'false',
        searchEngine: process.env.GOC_SEARCH_ENGINE || 'duckduckgo',
        maxResults: parseInt(process.env.GOC_MAX_SEARCH_RESULTS || '10'),
        timeout: parseInt(process.env.GOC_WEB_TIMEOUT || '30000'),
        userAgent: process.env.GOC_USER_AGENT || 'GOC-Agent/1.0',
        enableCaching: process.env.GOC_WEB_CACHE !== 'false'
      }
    };
  }

  async saveConfig(config: Partial<GocConfig>, filePath?: string): Promise<void> {
    const targetPath = filePath || path.join(process.cwd(), '.gocrc.json');
    
    try {
      const content = JSON.stringify(config, null, 2);
      fs.writeFileSync(targetPath, content, 'utf8');
    } catch (error) {
      throw new Error(`Failed to save config to ${targetPath}: ${error}`);
    }
  }
}
