"use strict";
/**
 * Configuration Loader
 *
 * Loads configuration from various sources
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigLoader = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class ConfigLoader {
    constructor() {
        this.configPaths = [
            '.gocrc.json',
            '.gocrc.js',
            'goc.config.json',
            'goc.config.js'
        ];
    }
    async loadConfig(configPath) {
        if (configPath) {
            return this.loadFromFile(configPath);
        }
        // Try to find config file in current directory and parent directories
        let currentDir = process.cwd();
        while (currentDir !== path.dirname(currentDir)) {
            for (const configFile of this.configPaths) {
                const fullPath = path.join(currentDir, configFile);
                if (fs.existsSync(fullPath)) {
                    return this.loadFromFile(fullPath);
                }
            }
            currentDir = path.dirname(currentDir);
        }
        // Load from environment variables
        return this.loadFromEnvironment();
    }
    async loadFromFile(filePath) {
        try {
            const ext = path.extname(filePath);
            if (ext === '.json') {
                const content = fs.readFileSync(filePath, 'utf8');
                return JSON.parse(content);
            }
            else if (ext === '.js') {
                // Dynamic import for JS config files
                const configModule = await Promise.resolve(`${path.resolve(filePath)}`).then(s => __importStar(require(s)));
                return configModule.default || configModule;
            }
            return {};
        }
        catch (error) {
            console.warn(`Failed to load config from ${filePath}:`, error);
            return {};
        }
    }
    loadFromEnvironment() {
        return {
            ai: {
                defaultProvider: process.env.GOC_AI_PROVIDER || 'groq',
                defaultModel: process.env.GOC_AI_MODEL || 'llama-3.1-70b-versatile',
                temperature: parseFloat(process.env.GOC_AI_TEMPERATURE || '0.7'),
                maxTokens: parseInt(process.env.GOC_AI_MAX_TOKENS || '4096'),
                timeout: parseInt(process.env.GOC_AI_TIMEOUT || '30000'),
                providers: {}
            },
            global: {
                logLevel: process.env.GOC_LOG_LEVEL || 'info',
                cacheDirectory: process.env.GOC_CACHE_DIR || '.goc/cache',
                tempDirectory: process.env.GOC_TEMP_DIR || '.goc/temp',
                enableTelemetry: process.env.GOC_TELEMETRY !== 'false',
                autoUpdate: process.env.GOC_AUTO_UPDATE !== 'false'
            },
            context: {
                maxContextLines: parseInt(process.env.GOC_MAX_CONTEXT_LINES || '1000'),
                enableSemanticSearch: process.env.GOC_SEMANTIC_SEARCH !== 'false',
                cacheEnabled: process.env.GOC_CACHE_ENABLED !== 'false',
                cacheSize: parseInt(process.env.GOC_CACHE_SIZE || '100'),
                analysisDepth: process.env.GOC_ANALYSIS_DEPTH || 'medium'
            },
            files: {
                maxFileSize: parseInt(process.env.GOC_MAX_FILE_SIZE || '10485760'),
                allowedExtensions: (process.env.GOC_ALLOWED_EXTENSIONS || '.js,.ts,.jsx,.tsx,.py,.php,.html,.css,.md,.json').split(','),
                backupEnabled: process.env.GOC_BACKUP_ENABLED !== 'false',
                backupDirectory: process.env.GOC_BACKUP_DIR || '.goc/backups',
                autoSave: process.env.GOC_AUTO_SAVE !== 'false'
            },
            web: {
                enabled: process.env.GOC_WEB_ENABLED !== 'false',
                searchEngine: process.env.GOC_SEARCH_ENGINE || 'duckduckgo',
                maxResults: parseInt(process.env.GOC_MAX_SEARCH_RESULTS || '10'),
                timeout: parseInt(process.env.GOC_WEB_TIMEOUT || '30000'),
                userAgent: process.env.GOC_USER_AGENT || 'GOC-Agent/1.0',
                enableCaching: process.env.GOC_WEB_CACHE !== 'false'
            }
        };
    }
    async saveConfig(config, filePath) {
        const targetPath = filePath || path.join(process.cwd(), '.gocrc.json');
        try {
            const content = JSON.stringify(config, null, 2);
            fs.writeFileSync(targetPath, content, 'utf8');
        }
        catch (error) {
            throw new Error(`Failed to save config to ${targetPath}: ${error}`);
        }
    }
}
exports.ConfigLoader = ConfigLoader;
//# sourceMappingURL=config-loader.js.map