/**
 * Groq AI Provider
 * 
 * Implementation for Groq's fast inference API
 */

import { BaseAIProvider } from '../base-provider';
import { AIMessage, AIResponse, ChatOptions, ModelInfo, AIProviderError } from '../../types';

export class GroqProvider extends BaseAIProvider {
  constructor(
    apiKey: string,
    baseUrl: string = 'https://api.groq.com/openai/v1',
    defaultModel: string = 'llama-3.1-70b-versatile',
    timeout: number = 30000
  ) {
    super('groq', 'Groq', apiKey, baseUrl, timeout, defaultModel);
  }

  async chat(messages: AIMessage[], options?: ChatOptions): Promise<AIResponse> {
    this.validateMessages(messages);

    const requestBody = {
      model: options?.model || this.getDefaultModel(),
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: options?.temperature ?? 0.7,
      max_tokens: options?.maxTokens ?? 4096,
      stream: false
    };

    const response = await this.makeRequest(
      `${this.baseUrl}/chat/completions`,
      {
        method: 'POST',
        headers: this.buildHeaders(),
        body: JSON.stringify(requestBody)
      },
      async (res) => res.json()
    ) as any;

    if (!response.choices || response.choices.length === 0) {
      throw new AIProviderError('No response choices returned', this.name);
    }

    const choice = response.choices[0];
    if (!choice.message || !choice.message.content) {
      throw new AIProviderError('Invalid response format', this.name);
    }

    const result: AIResponse = {
      content: choice.message.content,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens
      } : undefined,
      model: response.model,
      provider: this.name,
      timestamp: new Date()
    };

    // Update usage statistics
    if (result.usage) {
      this.updateUsage(result.usage.totalTokens);
    }

    return result;
  }

  async streamChat(messages: AIMessage[], options?: ChatOptions, onChunk?: (chunk: string) => void): Promise<AIResponse> {
    // For now, implement streaming as regular chat
    // TODO: Implement actual streaming when needed
    return this.chat(messages, options);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await this.makeRequest(
        `${this.baseUrl}/models`,
        {
          method: 'GET',
          headers: this.buildHeaders()
        },
        async (res) => res.json()
      ) as any;

      return response && Array.isArray(response.data);
    } catch (error) {
      return false;
    }
  }

  async getModels(): Promise<string[]> {
    try {
      const response = await this.makeRequest(
        `${this.baseUrl}/models`,
        {
          method: 'GET',
          headers: this.buildHeaders()
        },
        async (res) => res.json()
      ) as any;

      if (!response.data || !Array.isArray(response.data)) {
        return this.getDefaultModelIds();
      }

      return response.data.map((model: any) => model.id);
    } catch (error) {
      return this.getDefaultModelIds();
    }
  }

  override getDefaultModel(): string {
    return 'llama-3.1-70b-versatile';
  }

  private getDefaultModelIds(): string[] {
    return [
      'llama-3.1-70b-versatile',
      'llama-3.1-8b-instant',
      'mixtral-8x7b-32768'
    ];
  }

  private getDefaultModels(): ModelInfo[] {
    return [
      {
        id: 'llama-3.1-70b-versatile',
        name: 'Llama 3.1 70B Versatile',
        provider: this.name,
        tier: 'paid' as const,
        contextLength: 32768,
        capabilities: ['code-generation', 'code-explanation', 'code-review', 'general-chat'],
        isAvailable: true,
        lastChecked: new Date(),
        requiresAuth: true,
        isLocal: false
      },
      {
        id: 'llama-3.1-8b-instant',
        name: 'Llama 3.1 8B Instant',
        provider: this.name,
        tier: 'paid' as const,
        contextLength: 32768,
        capabilities: ['code-generation', 'general-chat'],
        isAvailable: true,
        lastChecked: new Date(),
        requiresAuth: true,
        isLocal: false
      },
      {
        id: 'mixtral-8x7b-32768',
        name: 'Mixtral 8x7B',
        provider: this.name,
        tier: 'paid' as const,
        contextLength: 32768,
        capabilities: ['code-generation', 'code-explanation', 'general-chat'],
        isAvailable: true,
        lastChecked: new Date(),
        requiresAuth: true,
        isLocal: false
      }
    ];
  }

  private getModelContextLength(modelId: string): number {
    const contextLengths: Record<string, number> = {
      'llama-3.1-70b-versatile': 32768,
      'llama-3.1-8b-instant': 32768,
      'mixtral-8x7b-32768': 32768,
      'gemma-7b-it': 8192
    };

    return contextLengths[modelId] || 4096;
  }

  private getModelCapabilities(modelId: string): string[] {
    const capabilities: Record<string, string[]> = {
      'llama-3.1-70b-versatile': ['code-generation', 'code-explanation', 'code-review', 'debugging', 'general-chat'],
      'llama-3.1-8b-instant': ['code-generation', 'general-chat'],
      'mixtral-8x7b-32768': ['code-generation', 'code-explanation', 'general-chat'],
      'gemma-7b-it': ['general-chat', 'code-generation']
    };

    return capabilities[modelId] || ['general-chat'];
  }
}
