"use strict";
/**
 * Intelligent Context Engine Module
 *
 * Provides advanced code analysis, semantic search, and context understanding capabilities
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextEngine = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const utils_1 = require("../utils");
class ContextEngine {
    constructor() {
        this.projectIndex = null;
        this.semanticAnalyzer = new SemanticAnalyzer();
        this.codeParser = new CodeParser();
        this.searchEngine = new SearchEngine();
    }
    /**
     * Enhanced code analysis with semantic understanding
     */
    async analyzeCode(code, options) {
        try {
            const language = options?.language || this.detectLanguage(options?.filePath || '');
            const lines = code.split('\n');
            // Basic metrics
            const metrics = {
                linesOfCode: lines.filter(line => line.trim().length > 0).length,
                complexity: this.calculateComplexity(code, language),
                maintainabilityIndex: this.calculateMaintainabilityIndex(code, language)
            };
            // Issues detection
            const issues = [];
            if (options?.includeMetrics) {
                issues.push(...this.detectIssues(code, language));
            }
            // Suggestions
            const suggestions = [];
            if (options?.includeSuggestions) {
                suggestions.push(...this.generateSuggestions(code, language, metrics));
            }
            // Dependencies
            const dependencies = await this.semanticAnalyzer.analyzeDependencies(code, language);
            // Summary
            const summary = this.generateAnalysisSummary(metrics, issues, suggestions);
            return {
                summary,
                issues,
                suggestions,
                metrics,
                dependencies
            };
        }
        catch (error) {
            utils_1.logger.error('Code analysis failed', error);
            return {
                summary: 'Code analysis failed',
                issues: [{ type: 'error', message: 'Analysis failed', severity: 1 }],
                suggestions: [],
                metrics: { linesOfCode: 0, complexity: 0, maintainabilityIndex: 0 },
                dependencies: []
            };
        }
    }
    /**
     * Get intelligent context for a file or location
     */
    async getContext(filePath, options) {
        try {
            if (!fs.existsSync(filePath)) {
                return [];
            }
            const content = fs.readFileSync(filePath, 'utf8');
            const language = this.detectLanguage(filePath);
            const lines = content.split('\n');
            const contextItems = [];
            const maxLines = options?.maxLines || 50;
            const targetLine = options?.line;
            if (targetLine) {
                // Get context around specific line
                const start = Math.max(0, targetLine - Math.floor(maxLines / 2));
                const end = Math.min(lines.length, start + maxLines);
                contextItems.push({
                    id: `${filePath}:${start}-${end}`,
                    type: 'file',
                    name: path.basename(filePath),
                    content: lines.slice(start, end).join('\n'),
                    filePath,
                    lineStart: start + 1,
                    lineEnd: end,
                    language,
                    relevanceScore: 1.0
                });
                // Add related symbols if requested
                if (options?.includeRelated && this.projectIndex) {
                    const relatedSymbols = this.findSymbolsInRange(filePath, start + 1, end);
                    for (const symbol of relatedSymbols) {
                        contextItems.push(this.symbolToContextItem(symbol));
                    }
                }
            }
            else {
                // Get file overview
                const symbols = await this.codeParser.parseFile(filePath, content);
                // Add main file content (truncated if needed)
                const truncatedContent = lines.length > maxLines ?
                    lines.slice(0, maxLines).join('\n') + '\n// ... truncated' :
                    content;
                contextItems.push({
                    id: filePath,
                    type: 'file',
                    name: path.basename(filePath),
                    content: truncatedContent,
                    filePath,
                    lineStart: 1,
                    lineEnd: Math.min(lines.length, maxLines),
                    language,
                    relevanceScore: 1.0
                });
                // Add important symbols
                for (const symbol of symbols.slice(0, 10)) { // Top 10 symbols
                    contextItems.push(this.symbolToContextItem(symbol));
                }
            }
            return contextItems;
        }
        catch (error) {
            utils_1.logger.error('Get context failed', error);
            return [];
        }
    }
    /**
     * Intelligent context search with semantic understanding
     */
    async searchContext(query, options) {
        try {
            const maxResults = options?.maxResults || 20;
            const results = [];
            if (options?.projectPath && !this.projectIndex) {
                // Quick index if not already indexed
                await this.indexProject(options.projectPath, { lightweight: true });
            }
            if (this.projectIndex) {
                // Use intelligent search
                const searchResults = await this.searchEngine.search(query, this.projectIndex, {
                    maxResults,
                    includeSemanticSearch: options?.includeSemanticSearch
                });
                for (const result of searchResults) {
                    results.push({
                        id: `${result.file}:${result.line}`,
                        type: 'file',
                        name: path.basename(result.file),
                        content: result.content,
                        filePath: result.file,
                        lineStart: result.line,
                        lineEnd: result.line + result.context.length,
                        language: this.detectLanguage(result.file),
                        relevanceScore: result.score
                    });
                }
            }
            else {
                // Fallback to simple text search
                if (options?.projectPath) {
                    const files = this.getProjectFiles(options.projectPath);
                    for (const file of files.slice(0, 10)) { // Limit for performance
                        const matches = await this.simpleTextSearch(file, query);
                        results.push(...matches.slice(0, 3)); // Top 3 matches per file
                    }
                }
            }
            return results.slice(0, maxResults);
        }
        catch (error) {
            utils_1.logger.error('Search context failed', error);
            return [];
        }
    }
    /**
     * Index a project for intelligent search and analysis
     */
    async indexProject(projectPath, options) {
        const startTime = Date.now();
        try {
            // Check if re-indexing is needed
            if (!options?.force && this.projectIndex && this.projectIndex.rootPath === projectPath) {
                const needsReindex = await this.needsReindexing(projectPath);
                if (!needsReindex) {
                    return {
                        success: true,
                        filesIndexed: 0,
                        symbolsFound: this.projectIndex.symbols.size,
                        embeddingsCreated: this.projectIndex.embeddings.size,
                        duration: Date.now() - startTime
                    };
                }
            }
            // Get files to index
            const files = this.getProjectFiles(projectPath, options?.excludePatterns);
            // Initialize project index
            this.projectIndex = {
                rootPath: projectPath,
                files,
                symbols: new Map(),
                embeddings: new Map(),
                dependencies: new Map(),
                lastIndexed: new Date(),
                version: '1.0.0'
            };
            let filesIndexed = 0;
            let symbolsFound = 0;
            let embeddingsCreated = 0;
            // Process files in batches for better performance
            const batchSize = options?.lightweight ? 5 : 10;
            for (let i = 0; i < files.length; i += batchSize) {
                const batch = files.slice(i, i + batchSize);
                await Promise.all(batch.map(async (file) => {
                    try {
                        const content = fs.readFileSync(file, 'utf8');
                        // Parse code and extract symbols
                        const symbols = await this.codeParser.parseFile(file, content);
                        symbols.forEach(symbol => {
                            this.projectIndex.symbols.set(`${symbol.file}:${symbol.name}`, symbol);
                            symbolsFound++;
                        });
                        // Create embeddings for code chunks (skip in lightweight mode)
                        if (!options?.lightweight) {
                            const embeddings = await this.createEmbeddings(file, content);
                            embeddings.forEach(embedding => {
                                this.projectIndex.embeddings.set(embedding.id, embedding);
                                embeddingsCreated++;
                            });
                        }
                        // Analyze dependencies
                        const language = this.detectLanguage(file);
                        const deps = await this.semanticAnalyzer.analyzeDependencies(content, language);
                        this.projectIndex.dependencies.set(file, deps);
                        filesIndexed++;
                    }
                    catch (error) {
                        utils_1.logger.warn(`Failed to index file ${file}`, error);
                    }
                }));
            }
            // Build cross-references
            if (!options?.lightweight) {
                await this.buildCrossReferences();
            }
            utils_1.logger.info(`Project indexed: ${filesIndexed} files, ${symbolsFound} symbols, ${embeddingsCreated} embeddings`);
            return {
                success: true,
                filesIndexed,
                symbolsFound,
                embeddingsCreated,
                duration: Date.now() - startTime
            };
        }
        catch (error) {
            utils_1.logger.error('Project indexing failed', error);
            return {
                success: false,
                filesIndexed: 0,
                symbolsFound: 0,
                embeddingsCreated: 0,
                duration: Date.now() - startTime,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Get intelligent context for a specific location
     */
    async getIntelligentContext(file, line, options) {
        if (!this.projectIndex) {
            throw new Error('Project not indexed. Call indexProject() first.');
        }
        // Find current symbol at location
        const currentSymbol = this.findSymbolAtLocation(file, line);
        // Get related symbols
        const relatedSymbols = currentSymbol ?
            await this.findRelatedSymbols(currentSymbol) : [];
        // Get dependencies
        const dependencies = this.projectIndex.dependencies.get(file) || [];
        // Find usages
        const usages = currentSymbol ?
            await this.findSymbolUsages(currentSymbol) : [];
        // Get context code
        const contextCode = await this.getContextCode(file, line, options?.contextLines || 10);
        // Generate suggestions
        const suggestions = await this.generateContextSuggestions(file, line, currentSymbol);
        return {
            currentSymbol,
            relatedSymbols,
            dependencies,
            usages,
            contextCode,
            suggestions
        };
    }
    /**
     * Analyze code relationships and patterns
     */
    async analyzeRelationships(filePath) {
        if (!this.projectIndex) {
            throw new Error('Project not indexed. Call indexProject() first.');
        }
        const content = fs.readFileSync(filePath, 'utf8');
        const language = this.detectLanguage(filePath);
        // Analyze imports and exports
        const depAnalysis = await this.semanticAnalyzer.getDetailedDependencyAnalysis(content, language);
        // Find dependents (files that depend on this file)
        const dependents = this.findDependents(filePath);
        // Calculate complexity
        const complexity = this.calculateComplexity(content, language);
        // Detect patterns
        const patterns = this.detectPatterns(content, language);
        return {
            imports: depAnalysis.imports,
            exports: depAnalysis.exports,
            dependencies: depAnalysis.internalDependencies,
            dependents,
            complexity,
            patterns
        };
    }
    /**
     * Get project statistics and insights
     */
    getProjectStats() {
        if (!this.projectIndex) {
            return null;
        }
        const languageBreakdown = {};
        const complexityMetrics = {};
        // Analyze embeddings for language breakdown
        for (const embedding of this.projectIndex.embeddings.values()) {
            languageBreakdown[embedding.language] = (languageBreakdown[embedding.language] || 0) + 1;
        }
        // Calculate complexity metrics
        complexityMetrics.averageSymbolsPerFile = this.projectIndex.symbols.size / this.projectIndex.files.length;
        complexityMetrics.averageEmbeddingsPerFile = this.projectIndex.embeddings.size / this.projectIndex.files.length;
        return {
            totalFiles: this.projectIndex.files.length,
            totalSymbols: this.projectIndex.symbols.size,
            totalEmbeddings: this.projectIndex.embeddings.size,
            languageBreakdown,
            complexityMetrics,
            lastIndexed: this.projectIndex.lastIndexed
        };
    }
    /**
     * Analyze entire project structure and provide comprehensive analysis
     */
    async analyzeProject(projectPath, options) {
        try {
            utils_1.logger.info('Starting comprehensive project analysis', { projectPath });
            // Index project if not already indexed
            if (!this.projectIndex || this.projectIndex.rootPath !== projectPath) {
                await this.indexProject(projectPath, { lightweight: !options?.includeSymbols });
            }
            const stats = this.getProjectStats();
            if (!stats) {
                throw new Error('Failed to get project statistics');
            }
            const result = {
                overview: {
                    name: path.basename(projectPath),
                    type: this.detectProjectType(projectPath),
                    files: stats.totalFiles,
                    languages: Object.keys(stats.languageBreakdown)
                }
            };
            // Include symbols if requested
            if (options?.includeSymbols && this.projectIndex) {
                result.symbols = {
                    functions: [],
                    classes: [],
                    interfaces: []
                };
                for (const symbol of this.projectIndex.symbols.values()) {
                    switch (symbol.type) {
                        case 'function':
                            result.symbols.functions.push({
                                name: symbol.name,
                                file: symbol.file,
                                line: symbol.line,
                                signature: symbol.signature,
                                description: symbol.documentation
                            });
                            break;
                        case 'class':
                            result.symbols.classes.push({
                                name: symbol.name,
                                file: symbol.file,
                                line: symbol.line,
                                signature: symbol.signature,
                                description: symbol.documentation
                            });
                            break;
                        case 'interface':
                            result.symbols.interfaces.push({
                                name: symbol.name,
                                file: symbol.file,
                                line: symbol.line,
                                signature: symbol.signature,
                                description: symbol.documentation
                            });
                            break;
                    }
                }
            }
            // Include relationships if requested
            if (options?.includeRelationships && this.projectIndex) {
                result.relationships = [];
                const mainFiles = this.projectIndex.files.slice(0, 10); // Analyze top 10 files
                for (const file of mainFiles) {
                    try {
                        const relationship = await this.analyzeRelationships(file);
                        result.relationships.push(relationship);
                    }
                    catch (error) {
                        utils_1.logger.warn(`Failed to analyze relationships for ${file}`, error);
                    }
                }
            }
            // Include metrics if requested
            if (options?.includeMetrics) {
                result.metrics = {
                    complexity: stats.complexityMetrics.averageSymbolsPerFile || 0,
                    maintainability: 85, // Default value
                    testCoverage: this.calculateTestCoverage(projectPath)
                };
            }
            utils_1.logger.info('Project analysis completed', {
                projectPath,
                filesAnalyzed: stats.totalFiles,
                symbolsFound: stats.totalSymbols
            });
            return result;
        }
        catch (error) {
            utils_1.logger.error('Project analysis failed', error);
            throw error;
        }
    }
    // Private helper methods
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.php': 'php',
            '.java': 'java',
            '.cs': 'csharp',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.rs': 'rust',
            '.go': 'go',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.dart': 'dart',
            '.vue': 'vue',
            '.svelte': 'svelte'
        };
        return languageMap[ext] || 'text';
    }
    calculateComplexity(code, language) {
        let complexity = 1; // Base complexity
        // Count decision points
        const decisionPatterns = [
            /\bif\b/g,
            /\belse\s+if\b/g,
            /\bwhile\b/g,
            /\bfor\b/g,
            /\bswitch\b/g,
            /\bcase\b/g,
            /\bcatch\b/g,
            /\b&&\b/g,
            /\b\|\|\b/g,
            /\?\s*:/g // Ternary operator
        ];
        decisionPatterns.forEach(pattern => {
            const matches = code.match(pattern);
            if (matches) {
                complexity += matches.length;
            }
        });
        return complexity;
    }
    calculateMaintainabilityIndex(code, language) {
        const lines = code.split('\n');
        const linesOfCode = lines.filter(line => line.trim().length > 0).length;
        const complexity = this.calculateComplexity(code, language);
        // Simplified maintainability index calculation
        // Real implementation would use Halstead metrics
        const volume = Math.log2(linesOfCode + 1) * linesOfCode;
        const maintainabilityIndex = Math.max(0, 171 - 5.2 * Math.log(volume) - 0.23 * complexity - 16.2 * Math.log(linesOfCode));
        return Math.round(maintainabilityIndex);
    }
    detectIssues(code, language) {
        const issues = [];
        const lines = code.split('\n');
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            const trimmed = line.trim();
            // Check for common issues based on language
            if (language === 'javascript' || language === 'typescript') {
                if (trimmed.includes('console.log') && !trimmed.startsWith('//')) {
                    issues.push({
                        type: 'warning',
                        message: 'console.log statement found - consider removing for production',
                        line: lineNum,
                        severity: 2
                    });
                }
                if (trimmed.includes('debugger') && !trimmed.startsWith('//')) {
                    issues.push({
                        type: 'warning',
                        message: 'debugger statement found - remove before production',
                        line: lineNum,
                        severity: 3
                    });
                }
            }
            if (language === 'python') {
                if (trimmed.includes('print(') && !trimmed.startsWith('#')) {
                    issues.push({
                        type: 'info',
                        message: 'print statement found - consider using logging',
                        line: lineNum,
                        severity: 1
                    });
                }
            }
            // Check for very long lines
            if (line.length > 120) {
                issues.push({
                    type: 'info',
                    message: 'Line is very long (>120 characters)',
                    line: lineNum,
                    column: 121,
                    severity: 1
                });
            }
            // Check for trailing whitespace
            if (line.endsWith(' ') || line.endsWith('\t')) {
                issues.push({
                    type: 'info',
                    message: 'Trailing whitespace',
                    line: lineNum,
                    column: line.length,
                    severity: 1
                });
            }
        });
        return issues;
    }
    generateSuggestions(code, language, metrics) {
        const suggestions = [];
        if (metrics.complexity > 10) {
            suggestions.push({
                type: 'maintainability',
                message: 'Consider breaking down complex functions into smaller ones',
                example: 'Extract logic into separate functions or use design patterns'
            });
        }
        if (metrics.maintainabilityIndex < 50) {
            suggestions.push({
                type: 'maintainability',
                message: 'Code maintainability is low - consider refactoring',
                example: 'Simplify complex expressions and improve naming'
            });
        }
        if (language === 'javascript' || language === 'typescript') {
            if (code.includes('var ')) {
                suggestions.push({
                    type: 'style',
                    message: 'Consider using const or let instead of var',
                    example: 'const value = 42; // or let value = 42;'
                });
            }
        }
        return suggestions;
    }
    generateAnalysisSummary(metrics, issues, suggestions) {
        const errorCount = issues.filter(i => i.type === 'error').length;
        const warningCount = issues.filter(i => i.type === 'warning').length;
        let summary = `Code analysis complete. ${metrics.linesOfCode} lines of code, complexity: ${metrics.complexity}`;
        if (errorCount > 0) {
            summary += `, ${errorCount} errors`;
        }
        if (warningCount > 0) {
            summary += `, ${warningCount} warnings`;
        }
        if (suggestions.length > 0) {
            summary += `, ${suggestions.length} suggestions`;
        }
        return summary;
    }
    async needsReindexing(projectPath) {
        // Simple implementation - in production, this would check file modification times
        return false;
    }
    getProjectFiles(projectPath, excludePatterns = []) {
        const defaultExcludes = [
            'node_modules',
            '.git',
            'vendor',
            'target',
            'build',
            'dist',
            '__pycache__',
            '.vscode',
            '.idea'
        ];
        const allExcludes = [...defaultExcludes, ...excludePatterns];
        const files = [];
        const walkDir = (dir) => {
            try {
                const entries = fs.readdirSync(dir);
                for (const entry of entries) {
                    if (allExcludes.includes(entry))
                        continue;
                    const fullPath = path.join(dir, entry);
                    const stat = fs.statSync(fullPath);
                    if (stat.isDirectory()) {
                        walkDir(fullPath);
                    }
                    else if (this.isCodeFile(entry)) {
                        files.push(fullPath);
                    }
                }
            }
            catch (error) {
                // Skip directories we can't read
            }
        };
        walkDir(projectPath);
        return files;
    }
    isCodeFile(filename) {
        const codeExtensions = [
            '.js', '.ts', '.jsx', '.tsx',
            '.py', '.pyw',
            '.php',
            '.java',
            '.cs',
            '.cpp', '.c', '.h',
            '.rs',
            '.go',
            '.rb',
            '.swift',
            '.kt',
            '.dart',
            '.vue',
            '.svelte'
        ];
        return codeExtensions.some(ext => filename.endsWith(ext));
    }
    async createEmbeddings(file, content) {
        // Simplified embedding creation - in production, this would use actual ML models
        const embeddings = [];
        const lines = content.split('\n');
        const chunkSize = 50; // Lines per chunk
        for (let i = 0; i < lines.length; i += chunkSize) {
            const chunk = lines.slice(i, i + chunkSize).join('\n');
            if (chunk.trim().length === 0)
                continue;
            // Create a simple hash-based embedding (placeholder)
            const hash = this.simpleHash(chunk);
            const embedding = Array.from({ length: 384 }, (_, idx) => Math.sin(hash + idx) * 0.1);
            embeddings.push({
                id: `${file}:${i}-${i + chunkSize}`,
                file,
                startLine: i + 1,
                endLine: Math.min(lines.length, i + chunkSize),
                content: chunk,
                embedding,
                symbols: [], // Would be populated with actual symbol extraction
                language: this.detectLanguage(file),
                hash: hash.toString()
            });
        }
        return embeddings;
    }
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash;
    }
    async buildCrossReferences() {
        if (!this.projectIndex)
            return;
        // Build symbol usage references
        for (const symbol of this.projectIndex.symbols.values()) {
            // Find usages of this symbol across the codebase
            const usages = await this.findSymbolUsages(symbol);
            symbol.usages = usages.map(usage => ({
                file: usage.file,
                line: usage.line,
                type: usage.type
            }));
        }
    }
    findSymbolAtLocation(file, line) {
        if (!this.projectIndex)
            return undefined;
        for (const symbol of this.projectIndex.symbols.values()) {
            if (symbol.file === file && symbol.line <= line) {
                return symbol;
            }
        }
        return undefined;
    }
    async findRelatedSymbols(symbol) {
        if (!this.projectIndex)
            return [];
        const related = [];
        // Find symbols in the same file
        for (const s of this.projectIndex.symbols.values()) {
            if (s.file === symbol.file && s.name !== symbol.name) {
                related.push(s);
            }
        }
        // Find symbols with similar names or in dependencies
        for (const s of this.projectIndex.symbols.values()) {
            if (symbol.dependencies.includes(s.name) || s.dependencies.includes(symbol.name)) {
                related.push(s);
            }
        }
        return related.slice(0, 10); // Limit results
    }
    async findSymbolUsages(symbol) {
        if (!this.projectIndex)
            return [];
        return await this.searchEngine.search(symbol.name, this.projectIndex, { maxResults: 20 });
    }
    async getContextCode(file, line, contextLines) {
        try {
            const content = fs.readFileSync(file, 'utf8');
            const lines = content.split('\n');
            const start = Math.max(0, line - contextLines);
            const end = Math.min(lines.length, line + contextLines);
            return lines.slice(start, end);
        }
        catch {
            return [];
        }
    }
    async generateContextSuggestions(file, line, symbol) {
        const suggestions = [];
        if (symbol) {
            suggestions.push(`Find all usages of ${symbol.name}`);
            suggestions.push(`Show definition of ${symbol.name}`);
            suggestions.push(`Find similar functions to ${symbol.name}`);
        }
        suggestions.push('Analyze file dependencies');
        suggestions.push('Show related code patterns');
        suggestions.push('Generate documentation');
        return suggestions;
    }
    findDependents(filePath) {
        if (!this.projectIndex)
            return [];
        const dependents = [];
        for (const [file, deps] of this.projectIndex.dependencies.entries()) {
            if (deps.some(dep => dep.includes(path.basename(filePath, path.extname(filePath))))) {
                dependents.push(file);
            }
        }
        return dependents;
    }
    detectPatterns(content, language) {
        const patterns = [];
        // Detect common patterns based on language
        if (language === 'javascript' || language === 'typescript') {
            if (/\(function\s*\([^)]*\)\s*{[\s\S]*}\)\s*\([^)]*\)/.test(content)) {
                patterns.push('IIFE (Immediately Invoked Function Expression)');
            }
            if (/new\s+Promise\s*\(|\.then\s*\(|\.catch\s*\(/.test(content)) {
                patterns.push('Promise Pattern');
            }
            if (/class\s+\w+\s+extends\s+\w+/.test(content)) {
                patterns.push('Class Inheritance');
            }
        }
        if (language === 'python') {
            if (/@\w+/.test(content)) {
                patterns.push('Decorator Pattern');
            }
            if (/with\s+\w+.*:/.test(content)) {
                patterns.push('Context Manager');
            }
        }
        return patterns;
    }
    findSymbolsInRange(file, startLine, endLine) {
        if (!this.projectIndex)
            return [];
        const symbols = [];
        for (const symbol of this.projectIndex.symbols.values()) {
            if (symbol.file === file && symbol.line >= startLine && symbol.line <= endLine) {
                symbols.push(symbol);
            }
        }
        return symbols;
    }
    symbolToContextItem(symbol) {
        return {
            id: `${symbol.file}:${symbol.name}`,
            type: symbol.type,
            name: symbol.name,
            content: symbol.signature || symbol.name,
            filePath: symbol.file,
            lineStart: symbol.line,
            lineEnd: symbol.line,
            language: this.detectLanguage(symbol.file),
            relevanceScore: 0.8
        };
    }
    async simpleTextSearch(file, query) {
        try {
            const content = fs.readFileSync(file, 'utf8');
            const lines = content.split('\n');
            const results = [];
            lines.forEach((line, index) => {
                if (line.toLowerCase().includes(query.toLowerCase())) {
                    results.push({
                        id: `${file}:${index + 1}`,
                        type: 'file',
                        name: path.basename(file),
                        content: line.trim(),
                        filePath: file,
                        lineStart: index + 1,
                        lineEnd: index + 1,
                        language: this.detectLanguage(file),
                        relevanceScore: 0.5
                    });
                }
            });
            return results;
        }
        catch {
            return [];
        }
    }
    detectProjectType(projectPath) {
        try {
            // Check for package.json (Node.js)
            if (fs.existsSync(path.join(projectPath, 'package.json'))) {
                return 'nodejs';
            }
            // Check for composer.json (PHP)
            if (fs.existsSync(path.join(projectPath, 'composer.json'))) {
                return 'php';
            }
            // Check for requirements.txt (Python)
            if (fs.existsSync(path.join(projectPath, 'requirements.txt'))) {
                return 'python';
            }
            // Check for pom.xml (Java)
            if (fs.existsSync(path.join(projectPath, 'pom.xml'))) {
                return 'java';
            }
            // Check for Cargo.toml (Rust)
            if (fs.existsSync(path.join(projectPath, 'Cargo.toml'))) {
                return 'rust';
            }
            return 'general';
        }
        catch {
            return 'general';
        }
    }
    calculateTestCoverage(projectPath) {
        try {
            // Simple heuristic: check for test files
            const files = this.getProjectFiles(projectPath);
            const testFiles = files.filter(file => file.includes('test') ||
                file.includes('spec') ||
                file.includes('__tests__'));
            const codeFiles = files.filter(file => this.isCodeFile(path.basename(file)));
            if (codeFiles.length === 0)
                return 0;
            // Rough estimate: if we have test files, assume some coverage
            return testFiles.length > 0 ? Math.min(80, (testFiles.length / codeFiles.length) * 100) : 0;
        }
        catch {
            return 0;
        }
    }
}
exports.ContextEngine = ContextEngine;
// Supporting classes
class SemanticAnalyzer {
    async analyzeDependencies(content, language) {
        const dependencies = [];
        switch (language) {
            case 'javascript':
            case 'typescript':
                dependencies.push(...this.analyzeJavaScriptDependencies(content));
                break;
            case 'python':
                dependencies.push(...this.analyzePythonDependencies(content));
                break;
            case 'php':
                dependencies.push(...this.analyzePHPDependencies(content));
                break;
            case 'java':
                dependencies.push(...this.analyzeJavaDependencies(content));
                break;
        }
        return [...new Set(dependencies)]; // Remove duplicates
    }
    async getDetailedDependencyAnalysis(content, language) {
        const analysis = {
            imports: [],
            exports: [],
            internalDependencies: [],
            externalDependencies: []
        };
        const allDeps = await this.analyzeDependencies(content, language);
        // Categorize dependencies
        allDeps.forEach(dep => {
            if (dep.startsWith('.') || dep.startsWith('/')) {
                analysis.internalDependencies.push(dep);
            }
            else {
                analysis.externalDependencies.push(dep);
            }
        });
        // Find exports (simplified)
        const exportMatches = content.match(/export\s+(?:default\s+)?(?:class|function|const|let|var)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g);
        if (exportMatches) {
            exportMatches.forEach(match => {
                const nameMatch = match.match(/([a-zA-Z_][a-zA-Z0-9_]*)\s*$/);
                if (nameMatch) {
                    analysis.exports.push(nameMatch[1]);
                }
            });
        }
        return analysis;
    }
    analyzeJavaScriptDependencies(content) {
        const dependencies = [];
        // ES6 imports
        const importMatches = content.match(/import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g);
        if (importMatches) {
            importMatches.forEach(match => {
                const moduleMatch = match.match(/from\s+['"`]([^'"`]+)['"`]/);
                if (moduleMatch) {
                    dependencies.push(moduleMatch[1]);
                }
            });
        }
        // CommonJS requires
        const requireMatches = content.match(/require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g);
        if (requireMatches) {
            requireMatches.forEach(match => {
                const moduleMatch = match.match(/['"`]([^'"`]+)['"`]/);
                if (moduleMatch) {
                    dependencies.push(moduleMatch[1]);
                }
            });
        }
        return dependencies;
    }
    analyzePythonDependencies(content) {
        const dependencies = [];
        // Standard imports
        const importMatches = content.match(/^import\s+([^\s#]+)/gm);
        if (importMatches) {
            importMatches.forEach(match => {
                const moduleMatch = match.match(/import\s+([^\s#]+)/);
                if (moduleMatch) {
                    dependencies.push(moduleMatch[1].split('.')[0]);
                }
            });
        }
        // From imports
        const fromMatches = content.match(/^from\s+([^\s#]+)\s+import/gm);
        if (fromMatches) {
            fromMatches.forEach(match => {
                const moduleMatch = match.match(/from\s+([^\s#]+)/);
                if (moduleMatch) {
                    dependencies.push(moduleMatch[1].split('.')[0]);
                }
            });
        }
        return dependencies;
    }
    analyzePHPDependencies(content) {
        const dependencies = [];
        // Use statements
        const useMatches = content.match(/use\s+([^;]+);/g);
        if (useMatches) {
            useMatches.forEach(match => {
                const classMatch = match.match(/use\s+([^;]+);/);
                if (classMatch) {
                    dependencies.push(classMatch[1].trim());
                }
            });
        }
        return dependencies;
    }
    analyzeJavaDependencies(content) {
        const dependencies = [];
        // Import statements
        const importMatches = content.match(/import\s+(?:static\s+)?([^;]+);/g);
        if (importMatches) {
            importMatches.forEach(match => {
                const classMatch = match.match(/import\s+(?:static\s+)?([^;]+);/);
                if (classMatch) {
                    dependencies.push(classMatch[1].trim());
                }
            });
        }
        return dependencies;
    }
}
class CodeParser {
    async parseFile(filePath, content) {
        const language = this.detectLanguage(filePath);
        const symbols = [];
        switch (language) {
            case 'javascript':
            case 'typescript':
                symbols.push(...this.parseJavaScript(filePath, content));
                break;
            case 'python':
                symbols.push(...this.parsePython(filePath, content));
                break;
            case 'php':
                symbols.push(...this.parsePHP(filePath, content));
                break;
        }
        return symbols;
    }
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.php': 'php',
            '.java': 'java'
        };
        return languageMap[ext] || 'text';
    }
    parseJavaScript(filePath, content) {
        const symbols = [];
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            // Function declarations
            const funcMatch = line.match(/(?:function\s+|const\s+|let\s+|var\s+)([a-zA-Z_][a-zA-Z0-9_]*)\s*(?:=\s*(?:function|async\s+function|\([^)]*\)\s*=>)|\([^)]*\))/);
            if (funcMatch) {
                symbols.push({
                    name: funcMatch[1],
                    type: 'function',
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(funcMatch[1]) + 1,
                    scope: 'global',
                    signature: line.trim(),
                    dependencies: [],
                    usages: []
                });
            }
            // Class declarations
            const classMatch = line.match(/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (classMatch) {
                symbols.push({
                    name: classMatch[1],
                    type: 'class',
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(classMatch[1]) + 1,
                    scope: 'global',
                    signature: line.trim(),
                    dependencies: [],
                    usages: []
                });
            }
        });
        return symbols;
    }
    parsePython(filePath, content) {
        const symbols = [];
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            // Function definitions
            const funcMatch = line.match(/def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/);
            if (funcMatch) {
                symbols.push({
                    name: funcMatch[1],
                    type: 'function',
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(funcMatch[1]) + 1,
                    scope: 'global',
                    signature: line.trim(),
                    dependencies: [],
                    usages: []
                });
            }
            // Class definitions
            const classMatch = line.match(/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (classMatch) {
                symbols.push({
                    name: classMatch[1],
                    type: 'class',
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(classMatch[1]) + 1,
                    scope: 'global',
                    signature: line.trim(),
                    dependencies: [],
                    usages: []
                });
            }
        });
        return symbols;
    }
    parsePHP(filePath, content) {
        const symbols = [];
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            const lineNum = index + 1;
            // Function definitions
            const funcMatch = line.match(/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/);
            if (funcMatch) {
                symbols.push({
                    name: funcMatch[1],
                    type: 'function',
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(funcMatch[1]) + 1,
                    scope: 'global',
                    signature: line.trim(),
                    dependencies: [],
                    usages: []
                });
            }
            // Class definitions
            const classMatch = line.match(/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (classMatch) {
                symbols.push({
                    name: classMatch[1],
                    type: 'class',
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf(classMatch[1]) + 1,
                    scope: 'global',
                    signature: line.trim(),
                    dependencies: [],
                    usages: []
                });
            }
        });
        return symbols;
    }
}
class SearchEngine {
    async search(query, projectIndex, options) {
        const results = [];
        const maxResults = options?.maxResults || 20;
        // Exact text search
        for (const file of projectIndex.files) {
            try {
                const content = fs.readFileSync(file, 'utf8');
                const lines = content.split('\n');
                lines.forEach((line, index) => {
                    if (line.toLowerCase().includes(query.toLowerCase())) {
                        results.push({
                            file,
                            line: index + 1,
                            column: line.toLowerCase().indexOf(query.toLowerCase()) + 1,
                            content: line.trim(),
                            score: 0.8,
                            type: 'exact',
                            context: [line.trim()],
                            symbols: []
                        });
                    }
                });
            }
            catch {
                // Skip files we can't read
            }
        }
        // Symbol search
        for (const symbol of projectIndex.symbols.values()) {
            if (symbol.name.toLowerCase().includes(query.toLowerCase())) {
                results.push({
                    file: symbol.file,
                    line: symbol.line,
                    column: symbol.column,
                    content: symbol.signature || symbol.name,
                    score: 0.9,
                    type: 'symbol',
                    context: [symbol.signature || symbol.name],
                    symbols: [symbol]
                });
            }
        }
        // Sort by score and return top results
        return results
            .sort((a, b) => b.score - a.score)
            .slice(0, maxResults);
    }
}
__exportStar(require("../types"), exports);
//# sourceMappingURL=index.js.map