/**
 * OpenAI Provider
 * 
 * Implementation for OpenAI's GPT models
 */

import { BaseAIProvider } from '../base-provider';
import { AIMessage, AIResponse, ChatOptions, ModelInfo, AIProviderError } from '../../types';

export class OpenAIProvider extends BaseAIProvider {
  constructor(
    apiKey: string,
    baseUrl: string = 'https://api.openai.com/v1',
    defaultModel: string = 'gpt-4',
    timeout: number = 30000
  ) {
    super('openai', 'OpenAI', apiKey, baseUrl, timeout, defaultModel);
  }

  async chat(messages: AIMessage[], options?: ChatOptions): Promise<AIResponse> {
    this.validateMessages(messages);

    const requestBody = {
      model: options?.model || this.getDefaultModel(),
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: options?.temperature ?? 0.7,
      max_tokens: options?.maxTokens ?? 4096
    };

    const response = await this.makeRequest(
      `${this.baseUrl}/chat/completions`,
      {
        method: 'POST',
        headers: this.buildHeaders(),
        body: JSON.stringify(requestBody)
      },
      async (res) => res.json()
    ) as any;

    if (!response.choices || response.choices.length === 0) {
      throw new AIProviderError('No response choices returned', this.name);
    }

    const choice = response.choices[0];
    if (!choice.message || !choice.message.content) {
      throw new AIProviderError('Invalid response format', this.name);
    }

    const result: AIResponse = {
      content: choice.message.content,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens
      } : undefined,
      model: response.model,
      provider: this.name,
      timestamp: new Date()
    };

    // Update usage statistics
    if (result.usage) {
      this.updateUsage(result.usage.totalTokens);
    }

    return result;
  }

  async streamChat(messages: AIMessage[], options?: ChatOptions, onChunk?: (chunk: string) => void): Promise<AIResponse> {
    // For now, implement streaming as regular chat
    // TODO: Implement actual streaming when needed
    return this.chat(messages, options);
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await this.makeRequest(
        `${this.baseUrl}/models`,
        {
          method: 'GET',
          headers: this.buildHeaders()
        },
        async (res) => res.json()
      ) as any;

      return response && Array.isArray(response.data);
    } catch (error) {
      return false;
    }
  }

  async getModels(): Promise<string[]> {
    return this.getDefaultModelIds();
  }

  override getDefaultModel(): string {
    return 'gpt-4';
  }

  private getDefaultModelIds(): string[] {
    return ['gpt-4', 'gpt-3.5-turbo'];
  }

  private getDefaultModels(): ModelInfo[] {
    return [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: this.name,
        tier: 'paid' as const,
        contextLength: 8192,
        capabilities: ['code-generation', 'code-explanation', 'code-review', 'debugging', 'general-chat'],
        isAvailable: true,
        lastChecked: new Date(),
        requiresAuth: true,
        isLocal: false
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: this.name,
        tier: 'paid' as const,
        contextLength: 4096,
        capabilities: ['code-generation', 'general-chat'],
        isAvailable: true,
        lastChecked: new Date(),
        requiresAuth: true,
        isLocal: false
      }
    ];
  }
}
