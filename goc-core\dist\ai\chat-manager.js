"use strict";
/**
 * Chat Manager
 *
 * Manages chat sessions and conversations with AI providers
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatManager = void 0;
const provider_factory_1 = require("./provider-factory");
class ChatManager {
    constructor() {
        this.sessions = new Map();
        this.providerFactory = provider_factory_1.AIProviderFactory.getInstance();
    }
    async startSession(sessionId) {
        const id = sessionId || this.generateSessionId();
        this.sessions.set(id, []);
        return id;
    }
    async sendMessage(sessionId, message, options) {
        if (!this.sessions.has(sessionId)) {
            throw new Error(`Session ${sessionId} not found`);
        }
        const history = this.sessions.get(sessionId);
        const userMessage = {
            role: 'user',
            content: message,
            timestamp: new Date()
        };
        history.push(userMessage);
        // Create provider with default configuration
        const provider = this.providerFactory.createProvider({
            name: 'goc',
            displayName: 'GOC Agent',
            tier: 'paid',
            apiKey: process.env.GROQ_API_KEY || '',
            baseUrl: 'https://api.groq.com/openai/v1',
            defaultModel: 'llama-3.1-70b-versatile',
            models: [{
                    id: 'llama-3.1-70b-versatile',
                    name: 'Llama 3.1 70B Versatile',
                    tier: 'paid',
                    contextLength: 32768,
                    capabilities: ['text-generation', 'code-generation'],
                    requiresAuth: true,
                    isLocal: false
                }],
            maxTokens: 4096,
            temperature: 0.7,
            timeout: 30000,
            requiresAuth: true,
            isLocal: false
        });
        const response = await provider.chat(history, options);
        const assistantMessage = {
            role: 'assistant',
            content: response.content,
            timestamp: new Date()
        };
        history.push(assistantMessage);
        return response;
    }
    getHistory(sessionId) {
        return this.sessions.get(sessionId) || [];
    }
    clearHistory(sessionId) {
        if (this.sessions.has(sessionId)) {
            this.sessions.set(sessionId, []);
        }
    }
    getSessions() {
        return Array.from(this.sessions.keys());
    }
    endSession(sessionId) {
        this.sessions.delete(sessionId);
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.ChatManager = ChatManager;
//# sourceMappingURL=chat-manager.js.map