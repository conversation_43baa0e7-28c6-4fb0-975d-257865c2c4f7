/**
 * Base AI Provider
 *
 * Abstract base class that provides common functionality for all AI providers
 */
/// <reference types="node" />
import { I<PERSON><PERSON>rovider, ProviderUsage } from './interfaces';
import { AIMessage, AIResponse, AIProviderConfig, ChatOptions, BaseResult } from '../types';
export declare abstract class BaseA<PERSON>rovider implements IAIProvider {
    readonly name: string;
    readonly displayName: string;
    protected apiKey: string;
    protected baseUrl: string;
    protected timeout: number;
    protected defaultModel: string;
    protected maxTokens: number;
    protected temperature: number;
    protected usage: ProviderUsage;
    protected isLocal: boolean;
    constructor(name: string, displayName: string, apiKey: string, baseUrl: string, timeout?: number, defaultModel?: string, maxTokens?: number, temperature?: number);
    get config(): AIProviderConfig;
    isAvailable(): Promise<boolean>;
    initialize(): Promise<void>;
    validateConnection(): Promise<BaseResult>;
    getUsage(): Promise<ProviderUsage>;
    dispose(): Promise<void>;
    abstract chat(messages: AIMessage[], options?: ChatOptions): Promise<AIResponse>;
    abstract streamChat(messages: AIMessage[], options?: ChatOptions, onChunk?: (chunk: string) => void): Promise<AIResponse>;
    abstract getModels(): Promise<string[]>;
    abstract validateApiKey(): Promise<boolean>;
    protected validateMessages(messages: AIMessage[]): void;
    protected buildHeaders(): Record<string, string>;
    protected makeRequest<T>(url: string, options: RequestInit, parser: (response: Response) => Promise<T>): Promise<T>;
    getDefaultModel(): string;
    protected updateUsage(tokens: number, cost?: number): void;
}
//# sourceMappingURL=base-provider.d.ts.map