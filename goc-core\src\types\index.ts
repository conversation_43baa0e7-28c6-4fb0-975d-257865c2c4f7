/**
 * Core Types for GOC Agent
 * 
 * Shared type definitions used across the entire GOC Agent ecosystem
 */

// Base types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
// Available AI providers - only Ollama (local) and GOC Agent (hosted)
export type AIProvider = 'ollama' | 'goc';
export type AnalysisDepth = 'shallow' | 'medium' | 'deep';
export type ModelTask = 'code-generation' | 'code-explanation' | 'code-review' | 'debugging' | 'refactoring' | 'documentation' | 'testing' | 'general-chat';

// Base result interface
export interface BaseResult {
  success: boolean;
  error?: string;
  data?: any;
  timestamp: Date;
}

// AI Message types
export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface AIResponse {
  content: string;
  role?: 'user' | 'assistant' | 'system';
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
  provider?: string;
  finishReason?: string;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

export interface ChatOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  model?: string;
  provider?: string;
  stream?: boolean;
}

// AI Tool types
export interface AITool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

// AI Model types
export type ModelTier = 'free' | 'paid' | 'freemium';
export type AuthenticationStatus = 'none' | 'required' | 'authenticated';

export interface ModelPricing {
  inputCostPer1k?: number;
  outputCostPer1k?: number;
  requestCost?: number;
  currency: string;
}

export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  tier: ModelTier;
  contextLength: number;
  capabilities: string[];
  isAvailable: boolean;
  lastChecked: Date;
  pricing?: ModelPricing;
  description?: string;
  requiresAuth: boolean;
  isLocal: boolean;
  installCommand?: string;
  size?: string;
}

export interface AIModel {
  id: string;
  name: string;
  tier: ModelTier;
  contextLength: number;
  capabilities: string[];
  pricing?: ModelPricing;
  description?: string;
  requiresAuth: boolean;
  isLocal: boolean;
  installCommand?: string;
  size?: string;
}

export interface AIProviderConfig {
  name: AIProvider;
  displayName: string;
  tier: ModelTier;
  apiKey?: string;
  baseUrl: string;
  models: AIModel[];
  defaultModel: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
  requiresAuth: boolean;
  isLocal: boolean;
  authenticationUrl?: string;
  setupInstructions?: string;
}

// Learning System Types
export interface LearningPattern {
  id: string;
  type: PatternType;
  content: string;
  context: string;
  frequency: number;
  confidence: number;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  lastUsed: Date;
  userId?: string;
  projectId?: string;
}

export interface UserPreference {
  id: string;
  userId: string;
  category: PreferenceCategory;
  key: string;
  value: any;
  confidence: number;
  learnedFrom: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface LearningEvent {
  id: string;
  type: LearningEventType;
  userId?: string;
  projectId?: string;
  data: Record<string, any>;
  patterns: string[];
  confidence: number;
  timestamp: Date;
}

export interface LearningMetrics {
  totalPatterns: number;
  activePatterns: number;
  userPreferences: number;
  learningEvents: number;
  averageConfidence: number;
  patternsByType: Record<PatternType, number>;
  recentActivity: LearningEvent[];
  topPatterns: LearningPattern[];
}

export type PatternType =
  | 'coding-style'
  | 'naming-convention'
  | 'architecture-pattern'
  | 'code-snippet'
  | 'project-structure'
  | 'dependency-usage'
  | 'error-solution'
  | 'best-practice'
  | 'user-workflow';

export type PreferenceCategory =
  | 'coding'
  | 'formatting'
  | 'architecture'
  | 'tools'
  | 'workflow'
  | 'ui'
  | 'ai-behavior';

export type LearningEventType =
  | 'code-generated'
  | 'code-accepted'
  | 'code-modified'
  | 'code-rejected'
  | 'pattern-applied'
  | 'preference-learned'
  | 'web-research'
  | 'context-built'
  | 'error-resolved';

// Configuration types
export interface GocConfig {
  version: string;
  ai: {
    defaultProvider: AIProvider;
    defaultModel: string;
    providers: Record<string, AIProviderConfig>;
    temperature: number;
    maxTokens: number;
    timeout: number;
  };
  context: {
    maxContextLines: number;
    enableSemanticSearch: boolean;
    cacheEnabled: boolean;
    cacheSize: number;
    analysisDepth: AnalysisDepth;
  };
  files: {
    maxFileSize: number;
    allowedExtensions: string[];
    backupEnabled: boolean;
    backupDirectory: string;
    autoSave: boolean;
  };
  web: {
    enabled: boolean;
    searchEngine: string;
    maxResults: number;
    timeout: number;
    userAgent: string;
    enableCaching: boolean;
  };
  learning: {
    enabled: boolean;
    autoLearn: boolean;
    maxPatterns: number;
    confidenceThreshold: number;
    retentionDays: number;
    enableWebLearning: boolean;
    enablePatternSharing: boolean;
    storageDirectory: string;
  };
  global: {
    logLevel: LogLevel;
    cacheDirectory: string;
    tempDirectory: string;
    enableTelemetry: boolean;
    autoUpdate: boolean;
  };
}

// Context types
export interface ContextItem {
  id: string;
  type: 'file' | 'function' | 'class' | 'variable' | 'import';
  name: string;
  content: string;
  filePath: string;
  lineStart: number;
  lineEnd: number;
  language: string;
  relevanceScore?: number;
}

export interface AnalysisResult {
  summary: string;
  issues: Issue[];
  suggestions: Suggestion[];
  metrics: CodeMetrics;
  dependencies: string[];
}

export interface Issue {
  type: 'error' | 'warning' | 'info';
  message: string;
  line?: number;
  column?: number;
  severity: number;
}

export interface Suggestion {
  type: 'performance' | 'style' | 'security' | 'maintainability';
  message: string;
  line?: number;
  example?: string;
}

export interface CodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainabilityIndex: number;
  testCoverage?: number;
}

// File types
export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  size: number;
  lastModified: Date;
  isDirectory: boolean;
  language?: string;
}

export interface FileContent {
  path: string;
  content: string;
  encoding: string;
  language: string;
  metadata?: Record<string, any>;
}

// Web types
export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  relevanceScore?: number;
}

export interface WebContent {
  url: string;
  title: string;
  content: string;
  metadata: Record<string, any>;
  timestamp: Date;
}

// Core options
export interface GocCoreOptions {
  ai?: Partial<GocConfig['ai']>;
  context?: Partial<GocConfig['context']>;
  files?: Partial<GocConfig['files']>;
  web?: Partial<GocConfig['web']>;
  learning?: Partial<GocConfig['learning']>;
  global?: Partial<GocConfig['global']>;
}

// Event types
export interface GocEvent {
  type: string;
  data: any;
  timestamp: Date;
}

export type EventHandler = (event: GocEvent) => void;

// Error types
export class GocError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'GocError';
  }
}

export class AIProviderError extends GocError {
  constructor(message: string, public provider: string, details?: any) {
    super(message, 'AI_PROVIDER_ERROR', details);
    this.name = 'AIProviderError';
  }
}

export class ConfigurationError extends GocError {
  constructor(message: string, details?: any) {
    super(message, 'CONFIGURATION_ERROR', details);
    this.name = 'ConfigurationError';
  }
}

export class FileOperationError extends GocError {
  constructor(message: string, public filePath: string, details?: any) {
    super(message, 'FILE_OPERATION_ERROR', details);
    this.name = 'FileOperationError';
  }
}
