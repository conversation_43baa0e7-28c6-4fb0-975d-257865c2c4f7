"use strict";
/**
 * Model Manager
 *
 * Manages AI models across different providers
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelManager = void 0;
const provider_factory_1 = require("./provider-factory");
class ModelManager {
    constructor() {
        this.modelCache = new Map();
        this.providerFactory = provider_factory_1.AIProviderFactory.getInstance();
    }
    async getAllModels() {
        const providers = this.providerFactory.getSupportedProviders();
        const allModels = [];
        for (const provider of providers) {
            try {
                const models = await this.getModelsForProvider(provider);
                allModels.push(...models);
            }
            catch (error) {
                console.warn(`Failed to get models for provider ${provider}:`, error);
            }
        }
        return allModels;
    }
    async getModelsForProvider(provider) {
        if (this.modelCache.has(provider)) {
            return this.modelCache.get(provider);
        }
        const models = this.getDefaultModelsForProvider(provider);
        this.modelCache.set(provider, models);
        return models;
    }
    async getModelInfo(provider, model) {
        const models = await this.getModelsForProvider(provider);
        return models.find(m => m.id === model) || null;
    }
    async isModelAvailable(provider, model) {
        const modelInfo = await this.getModelInfo(provider, model);
        return modelInfo?.isAvailable || false;
    }
    async getRecommendedModel(task) {
        const allModels = await this.getAllModels();
        // Filter models by task capability
        const suitableModels = allModels.filter(model => model.capabilities.includes(this.mapTaskToCapability(task)));
        if (suitableModels.length === 0) {
            return null;
        }
        // Return the first available model (could be enhanced with scoring)
        return suitableModels.find(model => model.isAvailable) || suitableModels[0];
    }
    getDefaultModelsForProvider(provider) {
        const now = new Date();
        switch (provider) {
            case 'goc':
                return [
                    {
                        id: 'llama-3.1-70b-versatile',
                        name: 'Llama 3.1 70B Versatile',
                        provider: 'goc',
                        tier: 'paid',
                        contextLength: 32768,
                        capabilities: ['code-generation', 'code-explanation', 'code-review', 'general-chat'],
                        isAvailable: true,
                        lastChecked: now,
                        requiresAuth: true,
                        isLocal: false
                    },
                    {
                        id: 'llama-3.1-8b-instant',
                        name: 'Llama 3.1 8B Instant',
                        provider: 'goc',
                        tier: 'paid',
                        contextLength: 32768,
                        capabilities: ['code-generation', 'general-chat'],
                        isAvailable: true,
                        lastChecked: now,
                        requiresAuth: true,
                        isLocal: false
                    }
                ];
            case 'openai':
                return [
                    {
                        id: 'gpt-4',
                        name: 'GPT-4',
                        provider: 'openai',
                        tier: 'paid',
                        contextLength: 8192,
                        capabilities: ['code-generation', 'code-explanation', 'code-review', 'debugging', 'general-chat'],
                        isAvailable: true,
                        lastChecked: now,
                        requiresAuth: true,
                        isLocal: false
                    },
                    {
                        id: 'gpt-3.5-turbo',
                        name: 'GPT-3.5 Turbo',
                        provider: 'openai',
                        tier: 'paid',
                        contextLength: 4096,
                        capabilities: ['code-generation', 'general-chat'],
                        isAvailable: true,
                        lastChecked: now,
                        requiresAuth: true,
                        isLocal: false
                    }
                ];
            case 'claude':
                return [
                    {
                        id: 'claude-3-sonnet',
                        name: 'Claude 3 Sonnet',
                        provider: 'claude',
                        tier: 'paid',
                        contextLength: 200000,
                        capabilities: ['code-generation', 'code-explanation', 'code-review', 'debugging', 'general-chat'],
                        isAvailable: true,
                        lastChecked: now,
                        requiresAuth: true,
                        isLocal: false
                    }
                ];
            case 'gemini':
                return [
                    {
                        id: 'gemini-pro',
                        name: 'Gemini Pro',
                        provider: 'gemini',
                        tier: 'paid',
                        contextLength: 32768,
                        capabilities: ['code-generation', 'code-explanation', 'general-chat'],
                        isAvailable: true,
                        lastChecked: now,
                        requiresAuth: true,
                        isLocal: false
                    }
                ];
            default:
                return [];
        }
    }
    mapTaskToCapability(task) {
        switch (task) {
            case 'code-generation':
            case 'code-explanation':
            case 'code-review':
            case 'debugging':
            case 'refactoring':
            case 'documentation':
            case 'testing':
                return task;
            case 'general-chat':
                return 'general-chat';
            default:
                return 'general-chat';
        }
    }
}
exports.ModelManager = ModelManager;
//# sourceMappingURL=model-manager.js.map