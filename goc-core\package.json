{"name": "@goc-agent/core", "version": "1.0.0", "description": "Core functionality library for GOC Agent ecosystem - AI providers, context engine, and shared utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "LICENSE"], "scripts": {"build": "tsc --skipL<PERSON><PERSON><PERSON><PERSON> --noStrictGenericChecks", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run build:watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "prepublishOnly": "npm run clean && npm run build", "docs": "typedoc src --out docs", "validate": "npm run lint && npm run test && npm run build"}, "keywords": ["ai", "coding-assistant", "goc-agent", "openai", "groq", "gemini", "claude", "context-engine", "code-analysis", "typescript"], "author": "GOC Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/goc-agent.git", "directory": "goc-core"}, "bugs": {"url": "https://github.com/your-org/goc-agent/issues"}, "homepage": "https://github.com/your-org/goc-agent/tree/main/goc-core#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"axios": "^1.6.0", "marked": "^9.1.0", "cheerio": "^1.0.0-rc.12", "node-fetch": "^3.3.2", "fs-extra": "^11.1.1", "glob": "^10.3.10", "mime-types": "^2.1.35", "yaml": "^2.3.4", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.8.0", "@types/jest": "^29.5.5", "@types/fs-extra": "^11.0.2", "@types/mime-types": "^2.1.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typedoc": "^0.25.2", "typescript": "^5.2.2"}, "peerDependencies": {"typescript": ">=4.9.0"}, "publishConfig": {"access": "public"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./ai": {"import": "./dist/ai/index.js", "require": "./dist/ai/index.js", "types": "./dist/ai/index.d.ts"}, "./context": {"import": "./dist/context/index.js", "require": "./dist/context/index.js", "types": "./dist/context/index.d.ts"}, "./files": {"import": "./dist/files/index.js", "require": "./dist/files/index.js", "types": "./dist/files/index.d.ts"}, "./web": {"import": "./dist/web/index.js", "require": "./dist/web/index.js", "types": "./dist/web/index.d.ts"}, "./config": {"import": "./dist/config/index.js", "require": "./dist/config/index.js", "types": "./dist/config/index.d.ts"}, "./utils": {"import": "./dist/utils/index.js", "require": "./dist/utils/index.js", "types": "./dist/utils/index.d.ts"}}}