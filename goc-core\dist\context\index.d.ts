/**
 * Intelligent Context Engine Module
 *
 * Provides advanced code analysis, semantic search, and context understanding capabilities
 */
import { ContextItem, AnalysisResult } from '../types';
export interface CodeSymbol {
    name: string;
    type: 'function' | 'class' | 'variable' | 'interface' | 'type' | 'method' | 'property';
    file: string;
    line: number;
    column: number;
    scope: string;
    signature?: string;
    documentation?: string;
    dependencies: string[];
    usages: Array<{
        file: string;
        line: number;
        type: 'call' | 'reference' | 'import';
    }>;
}
export interface CodeEmbedding {
    id: string;
    file: string;
    startLine: number;
    endLine: number;
    content: string;
    embedding: number[];
    symbols: string[];
    language: string;
    hash: string;
}
export interface SearchResult {
    file: string;
    line: number;
    column: number;
    content: string;
    score: number;
    type: 'exact' | 'semantic' | 'symbol' | 'usage';
    context: string[];
    symbols: CodeSymbol[];
}
export interface ProjectIndex {
    rootPath: string;
    files: string[];
    symbols: Map<string, CodeSymbol>;
    embeddings: Map<string, CodeEmbedding>;
    dependencies: Map<string, string[]>;
    lastIndexed: Date;
    version: string;
}
export interface ContextOptions {
    maxResults?: number;
    includeTests?: boolean;
    languages?: string[];
    minScore?: number;
    contextLines?: number;
}
export interface IndexingResult {
    success: boolean;
    filesIndexed: number;
    symbolsFound: number;
    embeddingsCreated: number;
    duration: number;
    error?: string;
}
export interface ContextAnalysis {
    currentSymbol?: CodeSymbol;
    relatedSymbols: CodeSymbol[];
    dependencies: string[];
    usages: SearchResult[];
    contextCode: string[];
    suggestions: string[];
}
export interface RelationshipAnalysis {
    imports: string[];
    exports: string[];
    dependencies: string[];
    dependents: string[];
    complexity: number;
    patterns: string[];
}
export interface ProjectStats {
    totalFiles: number;
    totalSymbols: number;
    totalEmbeddings: number;
    languageBreakdown: Record<string, number>;
    complexityMetrics: Record<string, number>;
    lastIndexed: Date;
}
export declare class ContextEngine {
    private projectIndex;
    private semanticAnalyzer;
    private codeParser;
    private searchEngine;
    constructor();
    /**
     * Enhanced code analysis with semantic understanding
     */
    analyzeCode(code: string, options?: {
        language?: string;
        includeMetrics?: boolean;
        includeSuggestions?: boolean;
        filePath?: string;
    }): Promise<AnalysisResult>;
    /**
     * Get intelligent context for a file or location
     */
    getContext(filePath: string, options?: {
        maxLines?: number;
        line?: number;
        includeRelated?: boolean;
    }): Promise<ContextItem[]>;
    /**
     * Intelligent context search with semantic understanding
     */
    searchContext(query: string, options?: {
        maxResults?: number;
        projectPath?: string;
        includeSemanticSearch?: boolean;
    }): Promise<ContextItem[]>;
    /**
     * Index a project for intelligent search and analysis
     */
    indexProject(projectPath: string, options?: {
        force?: boolean;
        incremental?: boolean;
        excludePatterns?: string[];
        lightweight?: boolean;
    }): Promise<IndexingResult>;
    /**
     * Get intelligent context for a specific location
     */
    getIntelligentContext(file: string, line: number, options?: ContextOptions): Promise<ContextAnalysis>;
    /**
     * Analyze code relationships and patterns
     */
    analyzeRelationships(filePath: string): Promise<RelationshipAnalysis>;
    /**
     * Get project statistics and insights
     */
    getProjectStats(): ProjectStats | null;
    /**
     * Analyze entire project structure and provide comprehensive analysis
     */
    analyzeProject(projectPath: string, options?: {
        includeSymbols?: boolean;
        includeRelationships?: boolean;
        includeMetrics?: boolean;
    }): Promise<{
        overview: {
            name: string;
            type: string;
            files: number;
            languages: string[];
        };
        symbols?: {
            functions: any[];
            classes: any[];
            interfaces: any[];
        };
        relationships?: RelationshipAnalysis[];
        metrics?: {
            complexity: number;
            maintainability: number;
            testCoverage: number;
        };
    }>;
    private detectLanguage;
    private calculateComplexity;
    private calculateMaintainabilityIndex;
    private detectIssues;
    private generateSuggestions;
    private generateAnalysisSummary;
    private needsReindexing;
    private getProjectFiles;
    private isCodeFile;
    private createEmbeddings;
    private simpleHash;
    private buildCrossReferences;
    private findSymbolAtLocation;
    private findRelatedSymbols;
    private findSymbolUsages;
    private getContextCode;
    private generateContextSuggestions;
    private findDependents;
    private detectPatterns;
    private findSymbolsInRange;
    private symbolToContextItem;
    private simpleTextSearch;
    private detectProjectType;
    private calculateTestCoverage;
}
export * from '../types';
//# sourceMappingURL=index.d.ts.map