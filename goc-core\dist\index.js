"use strict";
/**
 * GOC Core - Main Entry Point
 *
 * The core library for the GOC Agent ecosystem, providing AI providers,
 * context engine, file operations, web research, and configuration management.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createGocCore = exports.GocCore = exports.MonitoringService = exports.LearningEngine = exports.WebResearcher = exports.ContextEngine = void 0;
// Export all types
__exportStar(require("./types"), exports);
// Export all modules
__exportStar(require("./ai"), exports);
var context_1 = require("./context");
Object.defineProperty(exports, "ContextEngine", { enumerable: true, get: function () { return context_1.ContextEngine; } });
__exportStar(require("./files"), exports);
var web_1 = require("./web");
Object.defineProperty(exports, "WebResearcher", { enumerable: true, get: function () { return web_1.WebResearcher; } });
__exportStar(require("./config"), exports);
var learning_1 = require("./learning");
Object.defineProperty(exports, "LearningEngine", { enumerable: true, get: function () { return learning_1.LearningEngine; } });
__exportStar(require("./auto"), exports);
__exportStar(require("./generation"), exports);
var monitoring_1 = require("./monitoring");
Object.defineProperty(exports, "MonitoringService", { enumerable: true, get: function () { return monitoring_1.MonitoringService; } });
__exportStar(require("./documentation"), exports);
__exportStar(require("./deployment"), exports);
__exportStar(require("./utils"), exports);
const config_loader_1 = require("./config/config-loader");
const default_config_1 = require("./config/default-config");
const learning_engine_1 = require("./learning/learning-engine");
const context_2 = require("./context");
const web_2 = require("./web");
const auto_1 = require("./auto");
const generation_1 = require("./generation");
const monitoring_2 = require("./monitoring");
const documentation_1 = require("./documentation");
const deployment_1 = require("./deployment");
class GocCore {
    constructor(options) {
        this.initialized = false;
        this.configLoader = new config_loader_1.ConfigLoader();
        this.config = this.mergeConfig((0, default_config_1.getDefaultConfig)(), options);
    }
    async initialize() {
        // Load configuration from files
        const fileConfig = await this.configLoader.loadConfig();
        this.config = this.mergeConfig(this.config, fileConfig);
        // Initialize learning engine if enabled
        if (this.config.learning.enabled) {
            try {
                this.learningEngine = new learning_engine_1.LearningEngine({
                    config: this.config,
                    enableAutoLearning: this.config.learning.autoLearn
                });
                await this.learningEngine.initialize();
            }
            catch (error) {
                console.warn('Failed to initialize learning engine:', error);
            }
        }
        // Initialize context engine
        try {
            this.contextEngine = new context_2.ContextEngine();
        }
        catch (error) {
            console.warn('Failed to initialize context engine:', error);
            // Create a minimal stub
            this.contextEngine = {};
        }
        // Initialize web researcher
        try {
            this.webResearcher = new web_2.WebResearcher();
        }
        catch (error) {
            console.warn('Failed to initialize web researcher:', error);
            // Create a minimal stub
            this.webResearcher = {};
        }
        // Initialize code generator
        try {
            this.codeGenerator = new generation_1.CodeGenerator(this.contextEngine);
        }
        catch (error) {
            console.warn('Failed to initialize code generator:', error);
            // Create a minimal stub
            this.codeGenerator = {};
        }
        // Initialize auto mode engine
        try {
            this.autoModeEngine = new auto_1.AutoModeEngine(this.contextEngine, this.webResearcher, {
                enableLearning: this.config.learning.enabled,
                enableWebResearch: this.config.web.enabled,
                enableFileOperations: false, // Disabled by default for safety
                safeMode: true
            });
        }
        catch (error) {
            console.warn('Failed to initialize auto mode engine:', error);
            // Create a minimal stub
            this.autoModeEngine = {};
        }
        // Initialize monitoring system
        try {
            this.monitoringSystem = new monitoring_2.MonitoringSystem({
                enablePerformanceTracking: true,
                enableUsageAnalytics: true,
                enableErrorTracking: true,
                retentionDays: 30
            });
        }
        catch (error) {
            console.warn('Failed to initialize monitoring system:', error);
            // Create a minimal stub
            this.monitoringSystem = {};
        }
        // Initialize documentation generator
        try {
            this.documentationGenerator = new documentation_1.DocumentationGenerator(this.contextEngine);
        }
        catch (error) {
            console.warn('Failed to initialize documentation generator:', error);
            // Create a minimal stub
            this.documentationGenerator = {};
        }
        // Initialize deployment assistant
        try {
            this.deploymentAssistant = new deployment_1.DeploymentAssistant();
        }
        catch (error) {
            console.warn('Failed to initialize deployment assistant:', error);
            // Create a minimal stub
            this.deploymentAssistant = {};
        }
        this.initialized = true;
    }
    isInitialized() {
        return this.initialized;
    }
    async dispose() {
        if (this.learningEngine) {
            await this.learningEngine.dispose();
        }
        this.initialized = false;
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        this.config = this.mergeConfig(this.config, updates);
    }
    /**
     * Get the learning engine instance
     */
    getLearningEngine() {
        return this.learningEngine;
    }
    /**
     * Get the context engine instance
     */
    getContextEngine() {
        if (!this.contextEngine) {
            throw new Error('Context engine not initialized. Call initialize() first.');
        }
        return this.contextEngine;
    }
    /**
     * Get the web researcher instance
     */
    getWebResearcher() {
        if (!this.webResearcher) {
            throw new Error('Web researcher not initialized. Call initialize() first.');
        }
        return this.webResearcher;
    }
    /**
     * Get the auto mode engine instance
     */
    getAutoModeEngine() {
        if (!this.autoModeEngine) {
            throw new Error('Auto mode engine not initialized. Call initialize() first.');
        }
        return this.autoModeEngine;
    }
    /**
     * Get the code generator instance
     */
    getCodeGenerator() {
        if (!this.codeGenerator) {
            throw new Error('Code generator not initialized. Call initialize() first.');
        }
        return this.codeGenerator;
    }
    /**
     * Get the monitoring system instance
     */
    getMonitoringSystem() {
        if (!this.monitoringSystem) {
            throw new Error('Monitoring system not initialized. Call initialize() first.');
        }
        return this.monitoringSystem;
    }
    /**
     * Get the documentation generator instance
     */
    getDocumentationGenerator() {
        if (!this.documentationGenerator) {
            throw new Error('Documentation generator not initialized. Call initialize() first.');
        }
        return this.documentationGenerator;
    }
    /**
     * Get the deployment assistant instance
     */
    getDeploymentAssistant() {
        if (!this.deploymentAssistant) {
            throw new Error('Deployment assistant not initialized. Call initialize() first.');
        }
        return this.deploymentAssistant;
    }
    /**
     * Learn from AI interaction
     */
    async learnFromInteraction(messages, response, userFeedback) {
        if (this.learningEngine) {
            await this.learningEngine.learnFromInteraction(messages, response, userFeedback);
        }
    }
    /**
     * Enhance messages with learned context
     */
    async enhanceMessages(messages) {
        if (this.learningEngine) {
            return await this.learningEngine.enhanceMessages(messages);
        }
        return messages;
    }
    /**
     * Learn from web research
     */
    async learnFromWebResearch(query, results, relevantContent) {
        if (this.learningEngine) {
            await this.learningEngine.learnFromWebResearch(query, results, relevantContent);
        }
    }
    /**
     * Get learning metrics
     */
    async getLearningMetrics() {
        if (this.learningEngine) {
            return await this.learningEngine.getMetrics();
        }
        return null;
    }
    mergeConfig(base, updates) {
        if (!updates)
            return base;
        return {
            ...base,
            ai: updates.ai ? { ...base.ai, ...updates.ai } : base.ai,
            context: updates.context ? { ...base.context, ...updates.context } : base.context,
            files: updates.files ? { ...base.files, ...updates.files } : base.files,
            web: updates.web ? { ...base.web, ...updates.web } : base.web,
            learning: updates.learning ? { ...base.learning, ...updates.learning } : base.learning,
            global: updates.global ? { ...base.global, ...updates.global } : base.global
        };
    }
}
exports.GocCore = GocCore;
// Factory function for creating GOC Core instances
async function createGocCore(options) {
    const core = new GocCore(options);
    await core.initialize();
    return core;
}
exports.createGocCore = createGocCore;
//# sourceMappingURL=index.js.map