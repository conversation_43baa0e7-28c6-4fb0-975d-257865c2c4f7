"use strict";
/**
 * Context Enhancer
 *
 * Enhances AI prompts and messages with learned patterns and context
 * Works with all AI providers (OpenAI, Groq, Gemini, Ollama, etc.)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextEnhancer = void 0;
const knowledge_base_1 = require("./knowledge-base");
const utils_1 = require("../utils");
class ContextEnhancer {
    constructor(config) {
        this.initialized = false;
        this.config = config;
        this.knowledgeBase = new knowledge_base_1.KnowledgeBase(config);
    }
    async initialize() {
        if (this.initialized)
            return;
        try {
            utils_1.logger.info('Initializing Context Enhancer', 'ContextEnhancer');
            await this.knowledgeBase.initialize();
            this.initialized = true;
        }
        catch (error) {
            utils_1.logger.error('Failed to initialize Context Enhancer', 'ContextEnhancer', { error });
            throw error;
        }
    }
    /**
     * Enhance AI messages with learned context and patterns
     */
    async enhanceMessages(messages, context = {}) {
        if (!this.config.learning.enabled)
            return messages;
        try {
            const enhancementResult = await this.buildEnhancement(messages, context);
            return enhancementResult.enhancedMessages;
        }
        catch (error) {
            utils_1.logger.error('Failed to enhance messages', 'ContextEnhancer', { error });
            return messages;
        }
    }
    /**
     * Get detailed enhancement information
     */
    async enhanceWithDetails(messages, context = {}) {
        return await this.buildEnhancement(messages, context);
    }
    /**
     * Build comprehensive enhancement
     */
    async buildEnhancement(messages, context) {
        const appliedPatterns = [];
        const appliedPreferences = [];
        const enhancedMessages = [...messages];
        // Get user preferences
        const userPreferences = context.userId
            ? await this.knowledgeBase.getUserPreferences(context.userId)
            : [];
        // Get relevant patterns based on conversation content
        const relevantPatterns = await this.findRelevantPatterns(messages, context);
        // Enhance system message or create one
        const systemEnhancement = await this.buildSystemEnhancement(relevantPatterns, userPreferences, context);
        if (systemEnhancement.content.trim()) {
            // Find existing system message or create new one
            const systemMessageIndex = enhancedMessages.findIndex(m => m.role === 'system');
            if (systemMessageIndex >= 0) {
                // Enhance existing system message
                enhancedMessages[systemMessageIndex] = {
                    ...enhancedMessages[systemMessageIndex],
                    content: this.mergeSystemMessages(enhancedMessages[systemMessageIndex].content, systemEnhancement.content)
                };
            }
            else {
                // Add new system message at the beginning
                const systemMessage = {
                    role: 'system',
                    content: systemEnhancement.content,
                    metadata: {
                        patterns: systemEnhancement.patterns,
                        preferences: systemEnhancement.preferences
                    }
                };
                enhancedMessages.unshift(systemMessage);
            }
            appliedPatterns.push(...systemEnhancement.patterns);
            appliedPreferences.push(...systemEnhancement.preferences);
        }
        // Enhance user messages with context
        for (let i = 0; i < enhancedMessages.length; i++) {
            if (enhancedMessages[i].role === 'user') {
                const enhancement = await this.enhanceUserMessage(enhancedMessages[i], relevantPatterns, userPreferences, context);
                if (enhancement.enhanced) {
                    enhancedMessages[i] = enhancement.message;
                    appliedPatterns.push(...enhancement.patterns);
                    appliedPreferences.push(...enhancement.preferences);
                }
            }
        }
        const enhancementSummary = this.buildEnhancementSummary(appliedPatterns, appliedPreferences);
        return {
            enhancedMessages,
            appliedPatterns,
            appliedPreferences,
            enhancementSummary
        };
    }
    /**
     * Find relevant patterns for the conversation
     */
    async findRelevantPatterns(messages, context) {
        const conversationText = messages.map(m => m.content).join(' ');
        const keywords = this.extractKeywords(conversationText);
        const patterns = [];
        // Search for patterns using keywords
        for (const keyword of keywords.slice(0, 5)) { // Limit to top 5 keywords
            const foundPatterns = await this.knowledgeBase.searchPatterns(keyword, {
                userId: context.userId,
                projectId: context.projectId,
                limit: 3,
                minConfidence: 0.6
            });
            patterns.push(...foundPatterns);
        }
        // Get patterns by type based on conversation intent
        const intent = this.detectConversationIntent(conversationText);
        const intentPatterns = await this.getPatternsByIntent(intent, context);
        patterns.push(...intentPatterns);
        // Remove duplicates and sort by relevance
        const uniquePatterns = this.deduplicatePatterns(patterns);
        return uniquePatterns.slice(0, 10); // Limit to top 10 patterns
    }
    /**
     * Build system message enhancement
     */
    async buildSystemEnhancement(patterns, preferences, context) {
        const enhancements = [];
        const appliedPatterns = [];
        const appliedPreferences = [];
        // Add coding style preferences
        const codingPrefs = preferences.filter(p => p.category === 'coding');
        if (codingPrefs.length > 0) {
            const styleGuidelines = this.buildStyleGuidelines(codingPrefs);
            if (styleGuidelines) {
                enhancements.push(`Code Style Preferences:\n${styleGuidelines}`);
                appliedPreferences.push(...codingPrefs);
            }
        }
        // Add architectural patterns
        const archPatterns = patterns.filter(p => p.type === 'architecture-pattern');
        if (archPatterns.length > 0) {
            const archGuidelines = this.buildArchitecturalGuidelines(archPatterns);
            if (archGuidelines) {
                enhancements.push(`Preferred Architecture Patterns:\n${archGuidelines}`);
                appliedPatterns.push(...archPatterns);
            }
        }
        // Add naming conventions
        const namingPatterns = patterns.filter(p => p.type === 'naming-convention');
        if (namingPatterns.length > 0) {
            const namingGuidelines = this.buildNamingGuidelines(namingPatterns);
            if (namingGuidelines) {
                enhancements.push(`Naming Conventions:\n${namingGuidelines}`);
                appliedPatterns.push(...namingPatterns);
            }
        }
        // Add project context
        if (context.language || context.framework) {
            const projectContext = this.buildProjectContext(context);
            if (projectContext) {
                enhancements.push(`Project Context:\n${projectContext}`);
            }
        }
        // Add frequently used patterns
        const frequentPatterns = patterns
            .filter(p => p.frequency > 3)
            .slice(0, 3);
        if (frequentPatterns.length > 0) {
            const frequentGuidelines = this.buildFrequentPatternGuidelines(frequentPatterns);
            if (frequentGuidelines) {
                enhancements.push(`Frequently Used Patterns:\n${frequentGuidelines}`);
                appliedPatterns.push(...frequentPatterns);
            }
        }
        const content = enhancements.length > 0
            ? `Enhanced Context:\n\n${enhancements.join('\n\n')}`
            : '';
        return {
            content,
            patterns: appliedPatterns,
            preferences: appliedPreferences
        };
    }
    /**
     * Enhance user message with context
     */
    async enhanceUserMessage(message, patterns, preferences, context) {
        const enhancements = [];
        const appliedPatterns = [];
        const appliedPreferences = [];
        // Add relevant code snippets if user is asking for code
        if (this.isCodeRequest(message.content)) {
            const codePatterns = patterns.filter(p => p.type === 'code-snippet');
            if (codePatterns.length > 0) {
                const snippetContext = this.buildSnippetContext(codePatterns);
                if (snippetContext) {
                    enhancements.push(`Relevant code patterns: ${snippetContext}`);
                    appliedPatterns.push(...codePatterns.slice(0, 2));
                }
            }
        }
        // Add workflow context if this follows a pattern
        const workflowPatterns = patterns.filter(p => p.type === 'user-workflow');
        if (workflowPatterns.length > 0) {
            const workflowContext = this.buildWorkflowContext(workflowPatterns);
            if (workflowContext) {
                enhancements.push(`Based on your workflow: ${workflowContext}`);
                appliedPatterns.push(...workflowPatterns.slice(0, 1));
            }
        }
        const enhanced = enhancements.length > 0;
        const enhancedContent = enhanced
            ? `${message.content}\n\n[Context: ${enhancements.join('; ')}]`
            : message.content;
        return {
            enhanced,
            message: { ...message, content: enhancedContent },
            patterns: appliedPatterns,
            preferences: appliedPreferences
        };
    }
    // Helper methods
    extractKeywords(text) {
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);
        // Simple frequency analysis
        const frequency = new Map();
        for (const word of words) {
            frequency.set(word, (frequency.get(word) || 0) + 1);
        }
        return Array.from(frequency.entries())
            .sort((a, b) => b[1] - a[1])
            .map(([word]) => word)
            .slice(0, 10);
    }
    detectConversationIntent(text) {
        const lowerText = text.toLowerCase();
        if (lowerText.includes('create') || lowerText.includes('generate'))
            return 'create';
        if (lowerText.includes('fix') || lowerText.includes('debug'))
            return 'debug';
        if (lowerText.includes('explain') || lowerText.includes('how'))
            return 'explain';
        if (lowerText.includes('refactor') || lowerText.includes('improve'))
            return 'refactor';
        if (lowerText.includes('test'))
            return 'test';
        return 'general';
    }
    async getPatternsByIntent(intent, context) {
        const intentMap = {
            create: ['code-snippet', 'architecture-pattern'],
            debug: ['error-solution', 'best-practice'],
            explain: ['best-practice', 'architecture-pattern'],
            refactor: ['coding-style', 'architecture-pattern'],
            test: ['code-snippet', 'best-practice'],
            general: ['user-workflow', 'coding-style']
        };
        const relevantTypes = intentMap[intent] || ['coding-style'];
        const patterns = [];
        for (const type of relevantTypes) {
            const typePatterns = await this.knowledgeBase.getPatternsByType(type, {
                userId: context.userId,
                projectId: context.projectId,
                limit: 3
            });
            patterns.push(...typePatterns);
        }
        return patterns;
    }
    deduplicatePatterns(patterns) {
        const seen = new Set();
        return patterns.filter(pattern => {
            const key = `${pattern.type}-${pattern.context}-${pattern.content}`;
            if (seen.has(key))
                return false;
            seen.add(key);
            return true;
        });
    }
    buildStyleGuidelines(preferences) {
        const guidelines = [];
        for (const pref of preferences) {
            if (pref.key === 'indentation' && pref.confidence > 0.7) {
                guidelines.push(`- Use ${pref.value} for indentation`);
            }
            if (pref.key === 'brackets' && pref.confidence > 0.7) {
                guidelines.push(`- Use ${pref.value} bracket style`);
            }
            if (pref.key === 'naming' && pref.confidence > 0.7) {
                guidelines.push(`- Use ${pref.value} naming convention`);
            }
        }
        return guidelines.join('\n');
    }
    buildArchitecturalGuidelines(patterns) {
        const guidelines = patterns
            .filter(p => p.confidence > 0.6)
            .map(p => `- Prefer ${p.content} pattern`)
            .slice(0, 3);
        return guidelines.join('\n');
    }
    buildNamingGuidelines(patterns) {
        const guidelines = patterns
            .filter(p => p.confidence > 0.6)
            .map(p => `- Use ${p.content} for ${p.context}`)
            .slice(0, 3);
        return guidelines.join('\n');
    }
    buildProjectContext(context) {
        const contextParts = [];
        if (context.language)
            contextParts.push(`Language: ${context.language}`);
        if (context.framework)
            contextParts.push(`Framework: ${context.framework}`);
        if (context.currentFile)
            contextParts.push(`Current file: ${context.currentFile}`);
        return contextParts.join(', ');
    }
    buildFrequentPatternGuidelines(patterns) {
        return patterns
            .map(p => `- ${p.content} (used ${p.frequency} times)`)
            .join('\n');
    }
    buildSnippetContext(patterns) {
        return patterns
            .map(p => p.context)
            .join(', ');
    }
    buildWorkflowContext(patterns) {
        return patterns[0]?.content || '';
    }
    isCodeRequest(content) {
        const codeKeywords = ['create', 'generate', 'write', 'implement', 'code', 'function', 'class'];
        const lowerContent = content.toLowerCase();
        return codeKeywords.some(keyword => lowerContent.includes(keyword));
    }
    mergeSystemMessages(existing, enhancement) {
        return `${existing}\n\n${enhancement}`;
    }
    buildEnhancementSummary(patterns, preferences) {
        const parts = [];
        if (patterns.length > 0) {
            parts.push(`Applied ${patterns.length} learned patterns`);
        }
        if (preferences.length > 0) {
            parts.push(`Applied ${preferences.length} user preferences`);
        }
        return parts.join(', ') || 'No enhancements applied';
    }
    async dispose() {
        if (this.knowledgeBase) {
            await this.knowledgeBase.dispose();
        }
        this.initialized = false;
    }
}
exports.ContextEnhancer = ContextEnhancer;
//# sourceMappingURL=context-enhancer.js.map