/**
 * Ollama Provider
 * 
 * Local AI provider for Ollama models - completely offline and private
 */

import { BaseAIProvider } from '../base-provider';
import { AIMessage, AIResponse, ChatOptions, ModelInfo, AIProviderError, BaseResult } from '../../types';
import { logger } from '../../utils';

export class OllamaProvider extends BaseAIProvider {
  private availableModels: string[] = [];

  constructor(
    apiKey: string = '', // Not needed for Ollama
    baseUrl: string = 'http://localhost:11434',
    defaultModel: string = 'llama3.2:3b',
    timeout: number = 60000 // Longer timeout for local models
  ) {
    super('ollama', 'Ollama Local', apiKey, baseUrl, timeout, defaultModel);
    this.isLocal = true; // Ollama is always local
  }

  async initialize(): Promise<void> {
    try {
      // Check if Ollama is running and get available models
      await this.refreshAvailableModels();
      logger.info(`Ollama provider initialized with ${this.availableModels.length} models`, 'OllamaProvider');
    } catch (error) {
      logger.warn('Ollama not available - user needs to install and start Ollama', 'OllamaProvider');
      // Don't throw error - just log warning
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // Quick check
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async chat(messages: AIMessage[], options?: ChatOptions): Promise<AIResponse> {
    try {
      const model = options?.model || this.defaultModel;
      
      // Check if model is available
      if (!await this.isModelAvailable(model)) {
        throw new AIProviderError(
          `Model ${model} is not available. Please pull it first: ollama pull ${model}`,
          this.name
        );
      }

      const ollamaMessages = this.convertToOllamaFormat(messages);
      
      const requestBody = {
        model,
        messages: ollamaMessages,
        stream: false,
        options: {
          temperature: options?.temperature ?? 0.7,
          top_p: options?.topP ?? 0.9,
          max_tokens: options?.maxTokens ?? 4096,
        }
      };

      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.timeout)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new AIProviderError(
          `Ollama API error: ${response.status} - ${errorText}`,
          this.name
        );
      }

      const data = await response.json() as any;

      return {
        content: data.message?.content || '',
        role: 'assistant',
        model: model,
        usage: {
          promptTokens: data.prompt_eval_count || 0,
          completionTokens: data.eval_count || 0,
          totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
        },
        finishReason: data.done ? 'stop' : 'length',
        metadata: {
          provider: this.name,
          model: model,
          localExecution: true,
          privacy: 'complete',
          cost: 0 // Free local execution
        }
      };
    } catch (error) {
      if (error instanceof AIProviderError) {
        throw error;
      }
      throw new AIProviderError(
        `Failed to chat with Ollama: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.name,
        { originalError: error }
      );
    }
  }

  async streamChat(
    messages: AIMessage[], 
    options?: ChatOptions, 
    onChunk?: (chunk: string) => void
  ): Promise<AIResponse> {
    try {
      const model = options?.model || this.defaultModel;
      
      if (!await this.isModelAvailable(model)) {
        throw new AIProviderError(
          `Model ${model} is not available. Please pull it first: ollama pull ${model}`,
          this.name
        );
      }

      const ollamaMessages = this.convertToOllamaFormat(messages);
      
      const requestBody = {
        model,
        messages: ollamaMessages,
        stream: true,
        options: {
          temperature: options?.temperature ?? 0.7,
          top_p: options?.topP ?? 0.9,
          max_tokens: options?.maxTokens ?? 4096,
        }
      };

      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.timeout)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new AIProviderError(
          `Ollama API error: ${response.status} - ${errorText}`,
          this.name
        );
      }

      let fullContent = '';
      let totalTokens = 0;
      let promptTokens = 0;
      let completionTokens = 0;

      const reader = response.body?.getReader();
      if (!reader) {
        throw new AIProviderError('No response body available', this.name);
      }

      const decoder = new TextDecoder();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) break;
          
          const chunk = decoder.decode(value);
          const lines = chunk.split('\n').filter(line => line.trim());
          
          for (const line of lines) {
            try {
              const data = JSON.parse(line);
              
              if (data.message?.content) {
                const content = data.message.content;
                fullContent += content;
                onChunk?.(content);
              }
              
              if (data.prompt_eval_count) promptTokens = data.prompt_eval_count;
              if (data.eval_count) completionTokens = data.eval_count;
              
              if (data.done) {
                totalTokens = promptTokens + completionTokens;
                break;
              }
            } catch (parseError) {
              // Skip invalid JSON lines
              continue;
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      return {
        content: fullContent,
        role: 'assistant',
        model: model,
        usage: {
          promptTokens,
          completionTokens,
          totalTokens
        },
        finishReason: 'stop',
        metadata: {
          provider: this.name,
          model: model,
          localExecution: true,
          privacy: 'complete',
          cost: 0
        }
      };
    } catch (error) {
      if (error instanceof AIProviderError) {
        throw error;
      }
      throw new AIProviderError(
        `Failed to stream chat with Ollama: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.name,
        { originalError: error }
      );
    }
  }

  async getModels(): Promise<string[]> {
    await this.refreshAvailableModels();
    return this.availableModels;
  }

  async getDetailedModels(): Promise<Array<{
    name: string;
    size: string;
    digest: string;
    modified: string;
    details?: {
      format: string;
      family: string;
      families?: string[];
      parameter_size: string;
      quantization_level: string;
    };
  }>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(10000)
      });

      if (response.ok) {
        const data = await response.json() as any;
        return data.models || [];
      } else {
        return [];
      }
    } catch (error) {
      logger.error('Failed to get detailed models from Ollama', 'OllamaProvider', { error });
      return [];
    }
  }

  async getModelInfo(modelName: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/show`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: modelName }),
        signal: AbortSignal.timeout(10000)
      });

      if (response.ok) {
        return await response.json();
      } else {
        return null;
      }
    } catch (error) {
      logger.error(`Failed to get model info for ${modelName}`, 'OllamaProvider', { error });
      return null;
    }
  }

  async validateApiKey(): Promise<boolean> {
    // Ollama doesn't require API key validation
    return true;
  }

  async validateConnection(): Promise<BaseResult> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        return {
          success: false,
          error: 'Ollama is not running. Please start Ollama: ollama serve',
          timestamp: new Date()
        };
      }

      await this.refreshAvailableModels();

      if (this.availableModels.length === 0) {
        return {
          success: false,
          error: 'No models available. Please pull a model: ollama pull llama3.2:3b',
          timestamp: new Date()
        };
      }

      return {
        success: true,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      };
    }
  }

  async pullModel(modelName: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/pull`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: modelName }),
        signal: AbortSignal.timeout(300000) // 5 minutes for model download
      });

      return response.ok;
    } catch (error) {
      logger.error(`Failed to pull model ${modelName}`, 'OllamaProvider', { error });
      return false;
    }
  }

  async deleteModel(modelName: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: modelName })
      });

      if (response.ok) {
        await this.refreshAvailableModels();
      }

      return response.ok;
    } catch (error) {
      logger.error(`Failed to delete model ${modelName}`, 'OllamaProvider', { error });
      return false;
    }
  }

  getSetupInstructions(): string {
    return `
To use Ollama:

1. Install Ollama:
   - Visit: https://ollama.ai/download
   - Download and install for your OS

2. Start Ollama:
   - Run: ollama serve

3. Pull a model:
   - Run: ollama pull llama3.2:3b
   - Or: ollama pull codellama:7b

4. GOC Agent will automatically detect and use your local models

Benefits:
✅ 100% Private - Everything runs locally
✅ No internet required after setup
✅ Unlimited usage
✅ No API costs
✅ Full data privacy
    `;
  }

  // Private helper methods
  private async refreshAvailableModels(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        const data = await response.json() as any;
        this.availableModels = data.models?.map((model: any) => model.name) || [];

        // Log available models for debugging
        if (this.availableModels.length > 0) {
          logger.info(`Found ${this.availableModels.length} Ollama models: ${this.availableModels.join(', ')}`, 'OllamaProvider');
        } else {
          logger.warn('No Ollama models found. Run "ollama pull <model>" to install models', 'OllamaProvider');
        }
      } else {
        this.availableModels = [];
        logger.warn('Failed to fetch Ollama models - Ollama may not be running', 'OllamaProvider');
      }
    } catch (error) {
      this.availableModels = [];
      logger.warn('Ollama not available - install and start Ollama to use local models', 'OllamaProvider');
    }
  }

  private async isModelAvailable(modelName: string): Promise<boolean> {
    await this.refreshAvailableModels();
    return this.availableModels.includes(modelName);
  }

  private convertToOllamaFormat(messages: AIMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
  }
}
