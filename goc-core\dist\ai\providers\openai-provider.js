"use strict";
/**
 * OpenAI Provider
 *
 * Implementation for OpenAI's GPT models
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIProvider = void 0;
const base_provider_1 = require("../base-provider");
const types_1 = require("../../types");
class OpenAIProvider extends base_provider_1.BaseAIProvider {
    constructor(apiKey, baseUrl = 'https://api.openai.com/v1', defaultModel = 'gpt-4', timeout = 30000) {
        super('openai', 'OpenAI', apiKey, baseUrl, timeout, defaultModel);
    }
    async chat(messages, options) {
        this.validateMessages(messages);
        const requestBody = {
            model: options?.model || this.getDefaultModel(),
            messages: messages.map(msg => ({
                role: msg.role,
                content: msg.content
            })),
            temperature: options?.temperature ?? 0.7,
            max_tokens: options?.maxTokens ?? 4096
        };
        const response = await this.makeRequest(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: JSON.stringify(requestBody)
        }, async (res) => res.json());
        if (!response.choices || response.choices.length === 0) {
            throw new types_1.AIProviderError('No response choices returned', this.name);
        }
        const choice = response.choices[0];
        if (!choice.message || !choice.message.content) {
            throw new types_1.AIProviderError('Invalid response format', this.name);
        }
        const result = {
            content: choice.message.content,
            usage: response.usage ? {
                promptTokens: response.usage.prompt_tokens,
                completionTokens: response.usage.completion_tokens,
                totalTokens: response.usage.total_tokens
            } : undefined,
            model: response.model,
            provider: this.name,
            timestamp: new Date()
        };
        // Update usage statistics
        if (result.usage) {
            this.updateUsage(result.usage.totalTokens);
        }
        return result;
    }
    async streamChat(messages, options, onChunk) {
        // For now, implement streaming as regular chat
        // TODO: Implement actual streaming when needed
        return this.chat(messages, options);
    }
    async validateApiKey() {
        try {
            const response = await this.makeRequest(`${this.baseUrl}/models`, {
                method: 'GET',
                headers: this.buildHeaders()
            }, async (res) => res.json());
            return response && Array.isArray(response.data);
        }
        catch (error) {
            return false;
        }
    }
    async getModels() {
        return this.getDefaultModelIds();
    }
    getDefaultModel() {
        return 'gpt-4';
    }
    getDefaultModelIds() {
        return ['gpt-4', 'gpt-3.5-turbo'];
    }
    getDefaultModels() {
        return [
            {
                id: 'gpt-4',
                name: 'GPT-4',
                provider: this.name,
                tier: 'paid',
                contextLength: 8192,
                capabilities: ['code-generation', 'code-explanation', 'code-review', 'debugging', 'general-chat'],
                isAvailable: true,
                lastChecked: new Date(),
                requiresAuth: true,
                isLocal: false
            },
            {
                id: 'gpt-3.5-turbo',
                name: 'GPT-3.5 Turbo',
                provider: this.name,
                tier: 'paid',
                contextLength: 4096,
                capabilities: ['code-generation', 'general-chat'],
                isAvailable: true,
                lastChecked: new Date(),
                requiresAuth: true,
                isLocal: false
            }
        ];
    }
}
exports.OpenAIProvider = OpenAIProvider;
//# sourceMappingURL=openai-provider.js.map