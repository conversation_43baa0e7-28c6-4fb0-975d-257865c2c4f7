/**
 * Code Generation System
 *
 * Intelligent code generation with pattern recognition and best practices
 */
import { ContextEngine } from '../context';
export interface CodeTemplate {
    id: string;
    name: string;
    description: string;
    language: string;
    category: 'function' | 'class' | 'component' | 'module' | 'test' | 'config' | 'refactor';
    template: string;
    variables: Array<{
        name: string;
        type: string;
        description: string;
        default?: string;
        required: boolean;
    }>;
    dependencies: string[];
    examples: Array<{
        input: Record<string, any>;
        output: string;
    }>;
}
export interface GenerationRequest {
    type: 'function' | 'class' | 'component' | 'module' | 'test' | 'refactor';
    language: string;
    description: string;
    context?: {
        existingCode?: string;
        filePath?: string;
        projectType?: string;
        dependencies?: string[];
    };
    options?: {
        style?: 'modern' | 'traditional' | 'functional';
        includeTests?: boolean;
        includeDocumentation?: boolean;
        followPatterns?: boolean;
    };
}
export interface GenerationResult {
    code: string;
    explanation: string;
    suggestions: string[];
    tests?: string;
    documentation?: string;
    dependencies: string[];
    patterns: string[];
    quality: {
        score: number;
        issues: string[];
        improvements: string[];
    };
}
/**
 * Intelligent Code Generator
 */
export declare class CodeGenerator {
    private contextEngine;
    private templates;
    private patterns;
    constructor(contextEngine: ContextEngine);
    /**
     * Generate code based on description and context
     */
    generateCode(request: GenerationRequest): Promise<GenerationResult>;
    /**
     * Refactor existing code with improvements
     */
    refactorCode(code: string, options: {
        language: string;
        goals?: string[];
        preserveLogic?: boolean;
        modernize?: boolean;
    }): Promise<GenerationResult>;
    /**
     * Generate code from natural language description
     */
    generateFromDescription(description: string, language: string, context?: any): Promise<GenerationResult>;
    /**
     * Get available templates for a language
     */
    getTemplates(language?: string): CodeTemplate[];
    /**
     * Add custom template
     */
    addTemplate(template: CodeTemplate): void;
    private analyzeRequirements;
    private selectTemplate;
    private scoreTemplate;
    private generateFromTemplate;
    private extractVariables;
    private generateNameFromDescription;
    private formatCode;
    private formatJavaScript;
    private formatPython;
    private formatPHP;
    private generateTests;
    private getTestTemplate;
    private extractFunctionName;
    private generateTestCases;
    private generateDocumentation;
    private analyzeCodeQuality;
    private extractPatterns;
    private generateSuggestions;
    private generateExplanation;
    private parseIntent;
    private createBasicTemplate;
    private applyRefactoringPatterns;
    private modernizeCode;
    private reduceComplexity;
    private compareQuality;
    private initializeTemplates;
    private initializePatterns;
}
//# sourceMappingURL=index.d.ts.map