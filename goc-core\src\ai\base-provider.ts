/**
 * Base AI Provider
 * 
 * Abstract base class that provides common functionality for all AI providers
 */

import { IAIProvider, ProviderUsage } from './interfaces';
import { AIMessage, AIResponse, AIProviderConfig, ChatOptions, BaseResult } from '../types';

export abstract class BaseAIProvider implements IAIProvider {
  protected apiKey: string;
  protected baseUrl: string;
  protected timeout: number;
  protected defaultModel: string;
  protected maxTokens: number;
  protected temperature: number;
  protected usage: ProviderUsage;

  constructor(
    public readonly name: string,
    public readonly displayName: string,
    apiKey: string,
    baseUrl: string,
    timeout: number = 30000,
    defaultModel?: string,
    maxTokens: number = 4096,
    temperature: number = 0.7
  ) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.timeout = timeout;
    this.defaultModel = defaultModel || '';
    this.maxTokens = maxTokens;
    this.temperature = temperature;
    this.usage = {
      totalRequests: 0,
      totalTokens: 0,
      totalCost: 0,
      lastUsed: new Date(),
      averageResponseTime: 0
    };
  }

  get config(): AIProviderConfig {
    return {
      name: this.name as any, // Type assertion for provider name
      displayName: this.displayName,
      tier: this.isLocal ? 'free' : 'paid',
      apiKey: this.apiKey,
      baseUrl: this.baseUrl,
      models: [], // Will be populated by getModels()
      defaultModel: this.defaultModel,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      timeout: this.timeout,
      requiresAuth: !this.isLocal,
      isLocal: this.isLocal
    };
  }

  async isAvailable(): Promise<boolean> {
    try {
      const result = await this.validateConnection();
      return result.success;
    } catch {
      return false;
    }
  }

  async initialize(): Promise<void> {
    // Default implementation - can be overridden by providers
  }

  async validateConnection(): Promise<BaseResult> {
    try {
      const isValid = await this.validateApiKey();
      return {
        success: isValid,
        error: isValid ? undefined : 'Invalid API key or connection failed',
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      };
    }
  }

  async getUsage(): Promise<ProviderUsage> {
    return { ...this.usage };
  }

  async dispose(): Promise<void> {
    // Default implementation - can be overridden by providers
  }

  // Abstract methods that must be implemented by providers
  abstract chat(messages: AIMessage[], options?: ChatOptions): Promise<AIResponse>;
  abstract streamChat(messages: AIMessage[], options?: ChatOptions, onChunk?: (chunk: string) => void): Promise<AIResponse>;
  abstract getModels(): Promise<string[]>;
  abstract validateApiKey(): Promise<boolean>;

  // Protected utility methods for providers
  protected validateMessages(messages: AIMessage[]): void {
    if (!messages || messages.length === 0) {
      throw new Error('Messages array cannot be empty');
    }

    for (const message of messages) {
      if (!message.role || !message.content) {
        throw new Error('Each message must have role and content');
      }
      if (!['system', 'user', 'assistant'].includes(message.role)) {
        throw new Error(`Invalid message role: ${message.role}`);
      }
    }
  }

  protected buildHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
      'User-Agent': 'GOC-Agent/1.0.0'
    };
  }

  protected async makeRequest<T>(
    url: string,
    options: RequestInit,
    parser: (response: Response) => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: AbortSignal.timeout(this.timeout)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await parser(response);
      
      // Update usage statistics
      const duration = Date.now() - startTime;
      this.usage.totalRequests++;
      this.usage.lastUsed = new Date();
      this.usage.averageResponseTime = 
        (this.usage.averageResponseTime * (this.usage.totalRequests - 1) + duration) / this.usage.totalRequests;

      return result;
    } catch (error) {
      throw new Error(`Request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  getDefaultModel(): string {
    return this.defaultModel;
  }

  protected updateUsage(tokens: number, cost: number = 0): void {
    this.usage.totalTokens += tokens;
    this.usage.totalCost += cost;
  }
}
